<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="https://unpkg.com/aos@next/dist/aos.css" />
    <link rel="stylesheet" href="src/style.css" />

    <!-- font awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css"
      integrity="sha512-..."
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <title>Serv5</title>
    <!-- Custom styles for about page -->
    <style>
      /* About Sub Header Styles */
      .about-sub-header {
        position: relative;
        background: linear-gradient(135deg, #15205c 0%, #1e3a8c 100%);
        box-shadow: 0 10px 30px rgba(21, 32, 92, 0.2);
        transition: all 0.3s ease;
      }

      .about-sub-header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.5;
        pointer-events: none;
      }

      .about-sub-header nav ul li a {
        position: relative;
        overflow: hidden;
      }

      .about-sub-header nav ul li a::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 2px;
        background-color: #de8545;
        transition: all 0.3s ease;
        transform: translateX(-50%);
      }

      .about-sub-header nav ul li a:hover::after {
        width: 80%;
      }

      .about-cta-button {
        position: relative;
        overflow: hidden;
      }

      .about-cta-button::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s;
      }

      .about-cta-button:hover::before {
        left: 100%;
      }

      /* Responsive adjustments */
      @media (max-width: 768px) {
        .about-sub-header nav ul {
          max-width: 100%;
          overflow-x: auto;
          -webkit-overflow-scrolling: touch;
          padding-bottom: 8px;
        }

        .about-sub-header nav ul::-webkit-scrollbar {
          height: 4px;
        }

        .about-sub-header nav ul::-webkit-scrollbar-track {
          background: rgba(255, 255, 255, 0.1);
          border-radius: 4px;
        }

        .about-sub-header nav ul::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.3);
          border-radius: 4px;
        }
      }

      /* Business Links List Styles */
      .business-links-list {
        perspective: 1000px;
      }

      .business-link-item {
        transform-style: preserve-3d;
        cursor: pointer;
        position: relative;
        overflow: hidden;
      }

      .business-link-item::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(21, 32, 92, 0.1),
          transparent
        );
        transition: left 0.5s;
        z-index: 1;
      }

      .business-link-item:hover::before {
        left: 100%;
      }

      .business-link-item a {
        position: relative;
        z-index: 2;
      }

      .link-icon {
        box-shadow: 0 4px 12px rgba(21, 32, 92, 0.2);
        transition: all 0.3s ease;
      }

      .business-link-item:hover .link-icon {
        box-shadow: 0 6px 20px rgba(222, 133, 69, 0.4);
      }

      /* Responsive adjustments for business links */
      @media (max-width: 768px) {
        .business-link-item {
          padding: 1rem;
        }

        .link-icon {
          width: 28px;
          height: 28px;
        }

        .link-text {
          font-size: 0.9rem;
        }
      }

      /* Pin and Pin Hole Styles */
      .pin-container {
        position: relative;
        perspective: 1000px;
      }

      .pin-image {
        transform-origin: bottom center;
        transition: transform 0.3s ease;
      }

      .pin-container:hover .pin-image {
        transform: translateY(-5px) rotateX(10deg);
      }

      .pin-hole {
        background: radial-gradient(
          circle at center,
          rgba(0, 0, 0, 0.7) 0%,
          rgba(0, 0, 0, 0.5) 40%,
          rgba(0, 0, 0, 0.3) 70%,
          rgba(0, 0, 0, 0.1) 90%
        );
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.8),
          0 0 5px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(0, 0, 0, 0.2);
        transform: translateZ(-5px);
        position: relative;
      }

      .pin-hole::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60%;
        height: 60%;
        background: radial-gradient(
          circle at center,
          rgba(0, 0, 0, 0.9) 0%,
          rgba(0, 0, 0, 0.7) 50%,
          transparent 100%
        );
        border-radius: 50%;
      }

      .pin-hole::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: 50%;
        box-shadow: inset 0 1px 3px rgba(255, 255, 255, 0.2),
          inset 0 -2px 3px rgba(0, 0, 0, 0.4);
      }

      /* Vision Card Tilting Animation */
      .vision-card {
        transform-origin: top center;
        transform-style: preserve-3d;
        perspective: 1000px;
        transition: box-shadow 0.3s ease;
      }

      .vision-card:hover {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
      }

      /* Rocket Animation Styles */
      .rocket-container {
        perspective: 1000px;
        transform-style: preserve-3d;
      }

      .rocket-svg {
        transform-origin: center center;
        filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.2));
        transition: filter 0.3s ease;
      }

      .rocket-container:hover .rocket-svg {
        filter: drop-shadow(4px 8px 16px rgba(0, 0, 0, 0.3));
      }

      .rocket-flame-1,
      .rocket-flame-2 {
        transform-origin: top center;
        filter: drop-shadow(0 0 4px rgba(255, 127, 110, 0.6));
      }

      .rocket-exhaust-cloud {
        transform-origin: left center;
        filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
      }

      .rocket-window-outer,
      .rocket-window-inner {
        transition: fill 0.3s ease;
      }

      .rocket-nose {
        filter: drop-shadow(1px 2px 4px rgba(0, 0, 0, 0.2));
      }

      .rocket-body {
        filter: drop-shadow(2px 4px 6px rgba(0, 0, 0, 0.15));
      }

      /* Rocket launch trail effect */
      .rocket-container::after {
        content: "";
        position: absolute;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        width: 2px;
        height: 30px;
        background: linear-gradient(
          to bottom,
          rgba(255, 127, 110, 0.6),
          transparent
        );
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .rocket-container:hover::after {
        opacity: 1;
      }

      /* Envelope Animation Styles */
      .envelope-container {
        perspective: 1000px;
        transform-style: preserve-3d;
      }

      .envelope-svg {
        transform-origin: center center;
        filter: drop-shadow(3px 6px 12px rgba(0, 0, 0, 0.15));
        transition: filter 0.3s ease;
      }

      .envelope-container:hover .envelope-svg {
        filter: drop-shadow(5px 10px 20px rgba(0, 0, 0, 0.25));
      }

      .envelope-background {
        transform-origin: center center;
        filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.1));
      }

      .envelope-body {
        filter: drop-shadow(2px 4px 6px rgba(0, 0, 0, 0.12));
      }

      .envelope-flap-top {
        transform-origin: center top;
        filter: drop-shadow(1px 2px 4px rgba(0, 0, 0, 0.15));
      }

      .envelope-flap-left,
      .envelope-flap-right {
        transform-origin: center bottom;
        filter: drop-shadow(1px 3px 6px rgba(0, 0, 0, 0.12));
      }

      .envelope-stamp {
        transform-origin: center center;
        filter: drop-shadow(1px 2px 4px rgba(0, 0, 0, 0.2));
        transition: all 0.3s ease;
      }

      .envelope-container:hover .envelope-stamp {
        filter: drop-shadow(2px 4px 8px rgba(249, 174, 43, 0.4));
      }

      /* Envelope mail lines styling */
      .envelope-svg path[stroke="#B9D4DB"] {
        stroke-dasharray: 5, 3;
        animation: envelope-lines-float 3s ease-in-out infinite;
      }

      @keyframes envelope-lines-float {
        0%,
        100% {
          stroke-dashoffset: 0;
          opacity: 0.6;
        }
        50% {
          stroke-dashoffset: 8;
          opacity: 0.8;
        }
      }

      /* Future Vision Animation Styles */
      .future-vision-container {
        perspective: 1000px;
        transform-style: preserve-3d;
      }

      .future-vision-svg {
        transform-origin: center center;
        filter: drop-shadow(4px 8px 16px rgba(0, 0, 0, 0.2));
        transition: filter 0.3s ease;
      }

      .future-vision-container:hover .future-vision-svg {
        filter: drop-shadow(6px 12px 24px rgba(0, 0, 0, 0.3));
      }

      .person-face {
        transform-origin: center center;
        filter: drop-shadow(2px 4px 8px rgba(0, 0, 0, 0.15));
        transition: fill 0.3s ease;
      }

      .person-hair {
        transform-origin: center center;
        filter: drop-shadow(1px 3px 6px rgba(0, 0, 0, 0.2));
      }

      .vision-bubble-outer {
        transform-origin: center center;
        filter: drop-shadow(2px 4px 12px rgba(193, 213, 238, 0.4));
        animation: vision-bubble-glow 3s ease-in-out infinite;
      }

      .vision-bubble-inner {
        transform-origin: center center;
        filter: drop-shadow(1px 2px 8px rgba(36, 88, 167, 0.3));
      }

      .vision-eye {
        transform-origin: center center;
        filter: drop-shadow(0 0 6px rgba(255, 168, 0, 0.6));
        transition: all 0.3s ease;
      }

      .future-vision-container:hover .vision-eye {
        filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8));
      }

      @keyframes vision-bubble-glow {
        0%,
        100% {
          filter: drop-shadow(2px 4px 12px rgba(193, 213, 238, 0.4));
        }
        50% {
          filter: drop-shadow(3px 6px 18px rgba(193, 213, 238, 0.7));
        }
      }

      /* Vision lines animation */
      .future-vision-svg path[stroke="#203752"] {
        stroke-dasharray: 4, 2;
        animation: vision-lines-flow 2s linear infinite;
      }

      @keyframes vision-lines-flow {
        0% {
          stroke-dashoffset: 0;
          opacity: 0.7;
        }
        100% {
          stroke-dashoffset: 6;
          opacity: 0.9;
        }
      }
    </style>
  </head>

  <body
    class="flex flex-col gap-4 md:gap-8 lg:gap-12 2xl:gap-16 overflow-x-hidden"
  >
    <!-- start of the header -->
    <header class="bg-main-blue relative">
      <!-- start of the  logo -->
      <div class="container flex justify-between h-[127px] items-center">
        <svg
          width="101"
          height="98"
          class="size-[98px] header-logo"
          viewBox="0 0 101 98"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <!-- Outer circle (first to animate) -->
          <path
            id="logo-outer-circle"
            d="M84.9157 80.1711C76.8332 87.5147 65.6703 92.0596 53.3487 92.0596C28.7056 92.0596 8.69727 73.8802 8.69727 51.4897C8.69727 29.0993 28.7056 10.9199 53.3487 10.9199"
            stroke="#DE8545"
            stroke-width="4.6875"
            stroke-linecap="round"
            fill="none"
          />
          <!-- Inner circle (second to animate) -->
          <path
            id="logo-inner-circle"
            d="M53.3486 10.9199C77.9917 10.9199 98 29.0993 98 51.4897C98 73.8802 77.9917 92.0596 53.3486 92.0596C48.5334 92.0596 43.8953 91.3655 39.5488 90.0816"
            stroke="#DE8545"
            stroke-width="4.6875"
            stroke-linecap="round"
            fill="none"
          />
          <!-- Base rectangle (third to animate) -->
          <path
            id="logo-base-rectangle"
            d="M52.2359 98.0003H11.1552C5.01991 98.0003 0 93.4395 0 87.865C0 82.2905 5.01991 77.7295 11.1552 77.7295H52.2359C58.3713 77.7295 63.3911 82.2905 63.3911 87.865C63.3911 93.4395 58.3713 98.0003 52.2359 98.0003Z"
            fill="#15205C"
            stroke="#DE8545"
            stroke-width="0"
          />
          <!-- Text/Icons (fourth to animate) -->
          <g id="logo-text-icons">
            <path
              class="logo-letter"
              d="M14.4084 85.3265C14.1143 84.3605 13.5939 83.2712 12.1689 83.2712C10.9021 83.2712 10.3027 84.0831 10.3027 84.9051C10.3027 85.9635 11.0831 86.4774 12.4629 87.0837C13.8994 87.7208 15.3923 88.4299 15.3923 90.0845C15.3923 91.6978 13.8767 92.9824 11.5694 92.9824C10.8681 92.9824 10.3027 92.8694 9.87296 92.7358C9.44313 92.6124 9.20561 92.4891 9.03603 92.4171C8.92287 92.1191 8.67414 90.7524 8.57227 90.0227L9.08116 89.8891C9.3639 90.8449 10.1896 92.4171 11.9202 92.4171C13.1529 92.4171 13.8768 91.7286 13.8768 90.6495C13.8768 89.5603 12.972 89.0465 11.6826 88.4299C10.5063 87.8956 8.88894 87.1454 8.88894 85.4908C8.88894 83.9905 10.2235 82.7471 12.429 82.7471C13.2321 82.7471 13.9898 82.9218 14.6232 83.1068C14.691 83.6411 14.7702 84.2577 14.9172 85.2442L14.4084 85.3265Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M25.1869 90.3099C25.0738 90.8751 24.7457 92.2625 24.5987 92.7454H16.2969V92.3138C17.8238 92.2111 17.9708 92.0672 17.9708 90.834V84.9456C17.9708 83.5685 17.8238 83.5275 16.5118 83.4247V82.9727H24.1802C24.2028 83.363 24.2706 84.3599 24.3386 85.2025L23.8184 85.2643C23.66 84.6373 23.4789 84.288 23.2641 83.9591C23.0152 83.6303 22.5515 83.5275 21.3866 83.5275H20.131C19.5768 83.5275 19.543 83.5685 19.543 84.0207V87.3913H21.0925C22.608 87.3913 22.7211 87.2682 22.936 86.2507H23.445V89.2001H22.936C22.6986 88.1004 22.5741 88.0183 21.0925 88.0183H19.543V90.7826C19.543 91.4917 19.6335 91.8308 19.9501 92.0158C20.2781 92.1905 20.9115 92.1905 21.6806 92.1905C22.9134 92.1905 23.4223 92.0877 23.8069 91.6972C24.101 91.3787 24.4176 90.8545 24.6665 90.2482L25.1869 90.3099Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M36.2622 92.879C36.0586 92.879 35.8664 92.8688 35.6628 92.8482C34.2942 92.7865 33.593 92.4577 32.8126 91.4712C32.2358 90.7415 31.6816 89.8269 31.1726 89.1076C30.8672 88.6759 30.5732 88.491 29.7022 88.491H29.2273V90.834C29.2273 92.0979 29.3856 92.2111 30.7428 92.3138V92.7454H26.1621V92.3138C27.5307 92.2111 27.689 92.0979 27.689 90.834V84.8737C27.689 83.5891 27.542 83.5069 26.23 83.4247V82.9727H30.5618C31.8399 82.9727 32.6656 83.1062 33.3102 83.4966C34.0001 83.8872 34.43 84.5552 34.43 85.4697C34.43 86.7748 33.5026 87.5558 32.2923 88.0183C32.5864 88.5115 33.2763 89.4981 33.7853 90.1866C34.396 90.9779 34.7579 91.3992 35.1538 91.7692C35.5836 92.2111 35.9568 92.3755 36.3414 92.4577L36.2622 92.879ZM29.985 87.9874C30.8333 87.9874 31.3987 87.864 31.8286 87.5558C32.4732 87.1037 32.7335 86.5179 32.7335 85.7061C32.7335 84.0926 31.5685 83.4966 30.3129 83.4966C29.804 83.4966 29.5326 83.5584 29.4082 83.6405C29.295 83.7228 29.2273 83.8974 29.2273 84.288V87.9874H29.985Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M47.2054 83.4247C46.0518 83.5377 45.8141 83.7021 45.1468 85.0792C44.683 86.0555 42.9187 89.7961 41.6066 92.9098H41.0298C39.8875 90.1558 38.4511 86.9188 37.5011 84.7812C36.992 83.6612 36.7093 83.5069 35.6914 83.435V82.9727H40.0007V83.4247C38.7678 83.5377 38.7566 83.6713 39.0167 84.3496C39.4804 85.4286 40.7472 88.3573 41.8216 90.7723H41.8441C42.7716 88.7377 43.9593 85.9937 44.4795 84.6065C44.8188 83.7227 44.7283 83.5685 43.3484 83.4247V82.9727H47.2054V83.4247Z"
              fill="#DE8545"
            />
            <path
              class="logo-letter"
              d="M49.5919 87.3093C50.2818 87.0729 51.1075 86.8365 51.5485 86.8365C53.6974 86.8365 54.8173 88.1211 54.8173 89.3645C54.8173 90.2791 54.3309 91.1115 53.2791 91.8822C52.3629 92.5193 51.2206 92.9202 50.3157 92.9202C49.558 92.9202 48.8453 92.6221 48.574 92.3962C48.3251 92.1803 48.2346 92.057 48.2686 91.8616C48.2686 91.6973 48.4382 91.4609 48.6532 91.3068C48.8115 91.204 48.9472 91.204 49.1168 91.3274C49.49 91.6253 50.2026 92.1494 51.2545 92.1494C52.623 92.1494 53.2791 91.0807 53.2791 89.9914C53.2791 88.6555 52.3629 87.7306 50.5646 87.7306C49.8407 87.7306 49.1961 87.9155 48.8002 88.0286L49.4788 83.4248H54.3987L54.5005 83.5378L54.0368 84.6168H50.033L49.5919 87.3093Z"
              fill="#DE8545"
            />
            <path
              class="logo-icon"
              d="M55.4247 0H56.7964C56.8458 0.0246814 56.8921 0.0596832 56.945 0.0727212C59.5635 0.716344 60.7644 2.86669 59.7677 5.14651C59.2144 6.41208 58.6382 7.66938 58.0845 8.93474C57.8685 9.42846 57.6858 9.93419 57.4878 10.4344C57.407 10.5157 57.2931 10.5848 57.2509 10.6801C56.9786 11.2952 56.7216 11.9157 56.4603 12.5348C56.0419 13.5264 55.6241 14.5184 55.2061 15.5101C55.1242 15.6648 55.0325 15.8159 54.9619 15.9746C53.9279 18.2971 52.8966 20.6206 51.8647 22.9438C50.8327 25.2672 49.8065 27.5928 48.767 29.9134C48.0078 31.6082 46.1724 32.5722 44.3537 32.2499C41.7707 31.7922 40.3793 29.507 41.3334 27.2672C41.8675 26.0133 42.4286 24.7688 42.9671 23.544C42.8816 23.1901 42.8147 22.8671 42.7252 22.5494C42.2066 20.7099 41.4807 18.9575 40.1214 17.4737C39.5633 16.9424 39.0463 16.368 38.4381 15.8892C37.2331 14.9402 35.8555 14.2739 34.2565 14.0419C31.5012 13.5398 28.9524 13.9091 26.7367 15.5686C25.3568 16.6021 24.4801 17.8941 24.4541 19.5752C24.4129 19.7185 24.3246 19.8657 24.3389 20.0042C24.3947 20.5443 24.4363 21.0913 24.5686 21.618C24.8788 22.8525 25.4523 23.9791 26.391 24.934C26.5235 25.1351 26.629 25.3567 26.7927 25.5338C27.7984 26.6223 28.9601 27.5432 30.286 28.3095C31.9011 29.2429 33.4741 30.2363 35.0684 31.1997C36.1384 31.8463 37.2158 32.4827 38.2861 33.129C41.3919 35.004 44.5448 36.833 47.2642 39.168C48.9088 40.58 50.4845 42.0582 51.7042 43.8387C52.6133 45.1656 53.3738 46.5389 53.9229 48.0006C54.8725 50.5281 55.15 53.1352 54.8573 55.7992C54.4484 59.5217 53.0273 62.8719 50.2761 65.6639C44.3354 71.6926 36.7972 73.6354 28.1724 72.5071C26.3798 72.2726 24.6425 71.6955 22.8748 71.2943C22.1074 71.1202 21.3274 70.9917 20.6339 70.858C20.0577 71.2993 19.7734 71.7973 19.6408 72.4982C18.4475 74.6642 17.0417 75.3768 14.7209 74.9923C14.5521 74.919 14.3852 74.8417 14.2142 74.773C12.8143 74.2108 12.0251 73.218 11.8234 71.8615C11.7541 71.395 11.8539 70.9078 11.8764 70.4299C12.8938 68.1477 13.9111 65.8655 14.9285 63.5832C15.8547 61.5287 16.7885 59.477 17.7052 57.419C19.0576 54.3825 20.3931 51.3396 21.7453 48.303C22.3909 46.8529 23.5788 45.9991 25.3014 45.929C28.1445 45.8131 30.2687 48.261 29.1576 50.8921C27.7091 54.3216 26.1434 57.7101 24.6285 61.1165C24.3521 61.7382 24.0772 62.3605 23.7801 63.0314C24.2092 63.3795 24.5757 63.7598 25.0242 64.0258C26.6954 65.017 28.4817 65.7478 30.506 65.9144C34.0947 66.2098 37.2482 65.4815 39.5056 62.7212C41.5034 60.2783 41.8772 57.6559 40.4039 54.8734C39.5932 53.3425 38.4358 52.0404 37.1359 50.8381C34.6009 48.4935 31.6556 46.6094 28.7373 44.6965C25.5967 42.6377 22.4437 40.5943 19.316 38.5195C18.4452 37.9419 17.6447 37.2771 16.812 36.6522C16.2078 36.0812 15.5446 35.5518 15.0138 34.9293C14.2637 34.0495 13.6115 33.1009 12.9174 32.1817C12.9045 32.1137 12.9069 32.0394 12.8765 31.9786C11.8476 29.9147 11.4662 27.7315 11.4016 25.4855C11.3013 21.9913 12.0287 18.6743 14.0017 15.6403C16.244 12.1918 19.4768 9.77352 23.4851 8.1489C26.5217 6.91818 29.7003 6.28437 33.0012 6.13835C37.615 5.93425 42.0943 6.61149 46.4615 7.94561C47.4594 8.25054 48.4478 8.58132 49.4626 8.90727C50.3527 6.90236 51.28 4.95216 52.0829 2.9605C52.6895 1.45553 53.6803 0.399987 55.4247 0Z"
              fill="#2458A7"
            />
            <path
              class="logo-icon"
              d="M61.4293 17.3108H66.2065C68.3308 17.3197 70.4552 17.3357 72.5795 17.3357C74.7416 17.3356 76.9038 17.3196 79.0659 17.3105C80.1093 17.3105 81.1528 17.3105 82.2397 17.3105C82.4814 16.6209 82.7241 15.9357 82.9617 15.2491C83.6356 13.3025 85.8093 12.2428 87.9217 12.8328C90.0475 13.4266 91.2142 15.4589 90.548 17.4087C89.7385 19.778 88.9285 22.1472 88.1187 24.5164C87.5134 26.2267 86.908 27.937 86.3031 29.6473C86.2169 29.8911 86.1333 30.1356 86.0485 30.3797C86.0028 30.4334 85.9527 30.4845 85.9121 30.5412C84.7456 32.1723 82.6243 32.7534 80.7813 31.9456C78.9182 31.1292 78.0841 29.2351 78.762 27.384C79.0378 26.6307 79.2925 25.8709 79.5526 25.1128C79.6461 24.8404 79.7269 24.5645 79.8383 24.2116H66.8508C65.473 27.6652 64.0931 31.1242 62.6844 34.6553C62.9697 34.6116 63.1348 34.5891 63.2987 34.5608C67.7437 33.7909 72.1577 33.8927 76.5196 35.0112C82.5933 36.5687 87.6532 39.4903 91.2743 44.269C92.121 45.3864 92.8161 46.5986 93.581 47.7673C95.0354 50.8007 96.0776 53.9422 95.9567 57.2763C95.6538 65.6337 90.9819 72.7107 83.0579 77.1835C82.8326 77.3107 83.2826 77.0557 83.0579 77.1835C83.0075 77.2147 83.1082 77.1523 83.0579 77.1835C83.0179 77.2229 83.0979 77.1443 83.0579 77.1835C82.7679 77.285 83.3335 77.0565 83.0579 77.1835C79.4099 78.8651 74.6148 80.1694 70.5251 80.0128C62.0075 79.6865 55.0993 76.5363 50.0989 70.157C50.0774 70.1297 50.0675 70.0948 50.0293 70.0163C51.519 68.5236 53.0239 67.0157 54.5414 65.4952C55.1319 65.5267 55.7173 65.5579 56.1404 65.5805C57.2816 66.7397 58.2374 67.901 59.3971 68.8584C69.2744 77.0136 83.4219 72.2073 87.0103 62.1986C90.2255 53.2312 83.6993 43.3419 73.3653 41.5232C69.705 40.879 66.0924 41.1678 62.5053 41.92C60.9152 42.2534 59.353 42.6967 57.7781 43.0898C57.5969 43.1351 57.4151 43.1785 57.2096 43.2286C55.6099 41.9478 54.0152 40.6708 52.4036 39.3805C53.1658 37.4712 53.9153 35.5908 54.667 33.7109C56.4433 29.2687 58.2394 24.833 59.9756 20.378"
              fill="#DE8545"
            />
          </g>

          <!-- Gradient definition for shine effect -->
          <defs>
            <linearGradient
              id="shineGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              <stop offset="0%" style="stop-color: white; stop-opacity: 0" />
              <stop offset="50%" style="stop-color: white; stop-opacity: 0.2" />
              <stop offset="100%" style="stop-color: white; stop-opacity: 0" />
            </linearGradient>
          </defs>

          <!-- Shine effect overlay (hidden initially) -->
          <rect
            id="logo-shine"
            x="-20"
            y="0"
            width="20"
            height="98"
            fill="url(#shineGradient)"
            opacity="0"
          />
        </svg>

        <!-- end of the  logo -->

        <!-- links -->
        <ul class="hidden lg:flex items-center gap-10 text-white font-light">
          <!-- main -->
          <li
            class="nav-item active text-primary-yellow flex gap-1.5 items-center relative font-semibold transition-all duration-300"
          >
            <a href="/" class="flex gap-2 items-center">
              <i class="fa-solid fa-house"></i>
              <span data-nav="home">الرئيسية</span>
            </a>
            <!-- floating arrow -->
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[60px] h-[20px] absolute -bottom-5 left-1/2 animate-scale-x opacity-100"
            />
          </li>
          <!--نبذة عنا  -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-5"></i>
              <span data-nav="about">نبذة عنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- خدماتنا   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-plus"></i>
              <span data-nav="services">خدماتنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- أعمالــنا   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-briefcase"></i>
              <span data-nav="portfolio">أعمالــنا</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- المدونــة   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-regular fa-rectangle-list"></i>
              <span data-nav="blog">المدونــة</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
          <!-- الكتيب التعريفي   -->
          <li
            class="nav-item relative transition-all duration-300 hover:text-primary-yellow"
          >
            <a class="flex gap-2 items-center" href="/">
              <i class="fa-solid fa-file-pdf"></i>
              <span data-nav="brochure">الكتيب التعريفي</span>
            </a>
            <img
              src="/public/shared/header/yellow-arrow.webp"
              alt="Yellow arrow"
              class="nav-arrow w-[50px] h-[20px] absolute -bottom-3 left-1/2 animate-scale-x opacity-0 transition-opacity duration-300"
            />
          </li>
        </ul>
        <!-- Desktop Navigation -->
        <div class="hidden lg:flex gap-2 items-center">
          <!-- button to switch lang  -->
          <button
            id="langToggle"
            class="rounded-md text-primary-blue font-bold bg-white p-4 hover:bg-gray-100 transition-all duration-300 header-button"
          >
            EN
          </button>
          <!-- company profile button  -->
          <button
            class="rounded-md flex items-center gap-2 text-white border border-white font-bold bg-transparent p-4 hover:bg-white hover:text-primary-blue transition-all duration-300 header-button header-profile-btn"
          >
            <i class="fa-solid fa-book"></i>
            <span data-nav="profile">بروفايل الشركة</span>
          </button>

          <!-- our vision -->
          <img
            src="/public/shared/header/our-vision.webp"
            alt="Serv5 future vision for 2030"
            class="w-[125px] h-[84px] ms-8 header-vision"
          />
        </div>

        <!-- Mobile Navigation -->
        <div class="lg:hidden flex items-center gap-4">
          <!-- Mobile Language Toggle -->
          <button
            id="mobileLangToggle"
            class="rounded-md text-primary-blue font-bold bg-white p-3 hover:bg-gray-100 transition-all duration-300 text-sm"
          >
            EN
          </button>

          <!-- Burger Menu Button -->
          <button
            id="burgerMenuBtn"
            class="relative w-8 h-8 flex flex-col justify-center items-center space-y-1 focus:outline-none burger-menu-btn"
            aria-label="Toggle mobile menu"
          >
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
            <span
              class="burger-line w-6 h-0.5 bg-white transition-all duration-300 ease-in-out"
            ></span>
          </button>
        </div>
      </div>

      <!-- Mobile Menu Overlay -->
      <div
        id="mobileMenuOverlay"
        class="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden opacity-0 pointer-events-none transition-opacity duration-300"
      ></div>

      <!-- Mobile Menu -->
      <div
        id="mobileMenu"
        class="fixed top-0 w-80 h-full bg-main-blue z-50 lg:hidden transform transition-transform duration-300 ease-in-out mobile-menu"
      >
        <!-- Mobile Menu Header -->
        <div
          class="flex items-center justify-between p-6 border-b border-gray-700"
        >
          <div class="flex items-center gap-3">
            <svg
              width="101"
              height="98"
              class="size-[98px] header-logo"
              viewBox="0 0 101 98"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <!-- Outer circle (first to animate) -->
              <path
                id="logo-outer-circle"
                d="M84.9157 80.1711C76.8332 87.5147 65.6703 92.0596 53.3487 92.0596C28.7056 92.0596 8.69727 73.8802 8.69727 51.4897C8.69727 29.0993 28.7056 10.9199 53.3487 10.9199"
                stroke="#DE8545"
                stroke-width="4.6875"
                stroke-linecap="round"
                fill="none"
              />
              <!-- Inner circle (second to animate) -->
              <path
                id="logo-inner-circle"
                d="M53.3486 10.9199C77.9917 10.9199 98 29.0993 98 51.4897C98 73.8802 77.9917 92.0596 53.3486 92.0596C48.5334 92.0596 43.8953 91.3655 39.5488 90.0816"
                stroke="#DE8545"
                stroke-width="4.6875"
                stroke-linecap="round"
                fill="none"
              />
              <!-- Base rectangle (third to animate) -->
              <path
                id="logo-base-rectangle"
                d="M52.2359 98.0003H11.1552C5.01991 98.0003 0 93.4395 0 87.865C0 82.2905 5.01991 77.7295 11.1552 77.7295H52.2359C58.3713 77.7295 63.3911 82.2905 63.3911 87.865C63.3911 93.4395 58.3713 98.0003 52.2359 98.0003Z"
                fill="#15205C"
                stroke="#DE8545"
                stroke-width="0"
              />
              <!-- Text/Icons (fourth to animate) -->
              <g id="logo-text-icons">
                <path
                  class="logo-letter"
                  d="M14.4084 85.3265C14.1143 84.3605 13.5939 83.2712 12.1689 83.2712C10.9021 83.2712 10.3027 84.0831 10.3027 84.9051C10.3027 85.9635 11.0831 86.4774 12.4629 87.0837C13.8994 87.7208 15.3923 88.4299 15.3923 90.0845C15.3923 91.6978 13.8767 92.9824 11.5694 92.9824C10.8681 92.9824 10.3027 92.8694 9.87296 92.7358C9.44313 92.6124 9.20561 92.4891 9.03603 92.4171C8.92287 92.1191 8.67414 90.7524 8.57227 90.0227L9.08116 89.8891C9.3639 90.8449 10.1896 92.4171 11.9202 92.4171C13.1529 92.4171 13.8768 91.7286 13.8768 90.6495C13.8768 89.5603 12.972 89.0465 11.6826 88.4299C10.5063 87.8956 8.88894 87.1454 8.88894 85.4908C8.88894 83.9905 10.2235 82.7471 12.429 82.7471C13.2321 82.7471 13.9898 82.9218 14.6232 83.1068C14.691 83.6411 14.7702 84.2577 14.9172 85.2442L14.4084 85.3265Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M25.1869 90.3099C25.0738 90.8751 24.7457 92.2625 24.5987 92.7454H16.2969V92.3138C17.8238 92.2111 17.9708 92.0672 17.9708 90.834V84.9456C17.9708 83.5685 17.8238 83.5275 16.5118 83.4247V82.9727H24.1802C24.2028 83.363 24.2706 84.3599 24.3386 85.2025L23.8184 85.2643C23.66 84.6373 23.4789 84.288 23.2641 83.9591C23.0152 83.6303 22.5515 83.5275 21.3866 83.5275H20.131C19.5768 83.5275 19.543 83.5685 19.543 84.0207V87.3913H21.0925C22.608 87.3913 22.7211 87.2682 22.936 86.2507H23.445V89.2001H22.936C22.6986 88.1004 22.5741 88.0183 21.0925 88.0183H19.543V90.7826C19.543 91.4917 19.6335 91.8308 19.9501 92.0158C20.2781 92.1905 20.9115 92.1905 21.6806 92.1905C22.9134 92.1905 23.4223 92.0877 23.8069 91.6972C24.101 91.3787 24.4176 90.8545 24.6665 90.2482L25.1869 90.3099Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M36.2622 92.879C36.0586 92.879 35.8664 92.8688 35.6628 92.8482C34.2942 92.7865 33.593 92.4577 32.8126 91.4712C32.2358 90.7415 31.6816 89.8269 31.1726 89.1076C30.8672 88.6759 30.5732 88.491 29.7022 88.491H29.2273V90.834C29.2273 92.0979 29.3856 92.2111 30.7428 92.3138V92.7454H26.1621V92.3138C27.5307 92.2111 27.689 92.0979 27.689 90.834V84.8737C27.689 83.5891 27.542 83.5069 26.23 83.4247V82.9727H30.5618C31.8399 82.9727 32.6656 83.1062 33.3102 83.4966C34.0001 83.8872 34.43 84.5552 34.43 85.4697C34.43 86.7748 33.5026 87.5558 32.2923 88.0183C32.5864 88.5115 33.2763 89.4981 33.7853 90.1866C34.396 90.9779 34.7579 91.3992 35.1538 91.7692C35.5836 92.2111 35.9568 92.3755 36.3414 92.4577L36.2622 92.879ZM29.985 87.9874C30.8333 87.9874 31.3987 87.864 31.8286 87.5558C32.4732 87.1037 32.7335 86.5179 32.7335 85.7061C32.7335 84.0926 31.5685 83.4966 30.3129 83.4966C29.804 83.4966 29.5326 83.5584 29.4082 83.6405C29.295 83.7228 29.2273 83.8974 29.2273 84.288V87.9874H29.985Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M47.2054 83.4247C46.0518 83.5377 45.8141 83.7021 45.1468 85.0792C44.683 86.0555 42.9187 89.7961 41.6066 92.9098H41.0298C39.8875 90.1558 38.4511 86.9188 37.5011 84.7812C36.992 83.6612 36.7093 83.5069 35.6914 83.435V82.9727H40.0007V83.4247C38.7678 83.5377 38.7566 83.6713 39.0167 84.3496C39.4804 85.4286 40.7472 88.3573 41.8216 90.7723H41.8441C42.7716 88.7377 43.9593 85.9937 44.4795 84.6065C44.8188 83.7227 44.7283 83.5685 43.3484 83.4247V82.9727H47.2054V83.4247Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-letter"
                  d="M49.5919 87.3093C50.2818 87.0729 51.1075 86.8365 51.5485 86.8365C53.6974 86.8365 54.8173 88.1211 54.8173 89.3645C54.8173 90.2791 54.3309 91.1115 53.2791 91.8822C52.3629 92.5193 51.2206 92.9202 50.3157 92.9202C49.558 92.9202 48.8453 92.6221 48.574 92.3962C48.3251 92.1803 48.2346 92.057 48.2686 91.8616C48.2686 91.6973 48.4382 91.4609 48.6532 91.3068C48.8115 91.204 48.9472 91.204 49.1168 91.3274C49.49 91.6253 50.2026 92.1494 51.2545 92.1494C52.623 92.1494 53.2791 91.0807 53.2791 89.9914C53.2791 88.6555 52.3629 87.7306 50.5646 87.7306C49.8407 87.7306 49.1961 87.9155 48.8002 88.0286L49.4788 83.4248H54.3987L54.5005 83.5378L54.0368 84.6168H50.033L49.5919 87.3093Z"
                  fill="#DE8545"
                />
                <path
                  class="logo-icon"
                  d="M55.4247 0H56.7964C56.8458 0.0246814 56.8921 0.0596832 56.945 0.0727212C59.5635 0.716344 60.7644 2.86669 59.7677 5.14651C59.2144 6.41208 58.6382 7.66938 58.0845 8.93474C57.8685 9.42846 57.6858 9.93419 57.4878 10.4344C57.407 10.5157 57.2931 10.5848 57.2509 10.6801C56.9786 11.2952 56.7216 11.9157 56.4603 12.5348C56.0419 13.5264 55.6241 14.5184 55.2061 15.5101C55.1242 15.6648 55.0325 15.8159 54.9619 15.9746C53.9279 18.2971 52.8966 20.6206 51.8647 22.9438C50.8327 25.2672 49.8065 27.5928 48.767 29.9134C48.0078 31.6082 46.1724 32.5722 44.3537 32.2499C41.7707 31.7922 40.3793 29.507 41.3334 27.2672C41.8675 26.0133 42.4286 24.7688 42.9671 23.544C42.8816 23.1901 42.8147 22.8671 42.7252 22.5494C42.2066 20.7099 41.4807 18.9575 40.1214 17.4737C39.5633 16.9424 39.0463 16.368 38.4381 15.8892C37.2331 14.9402 35.8555 14.2739 34.2565 14.0419C31.5012 13.5398 28.9524 13.9091 26.7367 15.5686C25.3568 16.6021 24.4801 17.8941 24.4541 19.5752C24.4129 19.7185 24.3246 19.8657 24.3389 20.0042C24.3947 20.5443 24.4363 21.0913 24.5686 21.618C24.8788 22.8525 25.4523 23.9791 26.391 24.934C26.5235 25.1351 26.629 25.3567 26.7927 25.5338C27.7984 26.6223 28.9601 27.5432 30.286 28.3095C31.9011 29.2429 33.4741 30.2363 35.0684 31.1997C36.1384 31.8463 37.2158 32.4827 38.2861 33.129C41.3919 35.004 44.5448 36.833 47.2642 39.168C48.9088 40.58 50.4845 42.0582 51.7042 43.8387C52.6133 45.1656 53.3738 46.5389 53.9229 48.0006C54.8725 50.5281 55.15 53.1352 54.8573 55.7992C54.4484 59.5217 53.0273 62.8719 50.2761 65.6639C44.3354 71.6926 36.7972 73.6354 28.1724 72.5071C26.3798 72.2726 24.6425 71.6955 22.8748 71.2943C22.1074 71.1202 21.3274 70.9917 20.6339 70.858C20.0577 71.2993 19.7734 71.7973 19.6408 72.4982C18.4475 74.6642 17.0417 75.3768 14.7209 74.9923C14.5521 74.919 14.3852 74.8417 14.2142 74.773C12.8143 74.2108 12.0251 73.218 11.8234 71.8615C11.7541 71.395 11.8539 70.9078 11.8764 70.4299C12.8938 68.1477 13.9111 65.8655 14.9285 63.5832C15.8547 61.5287 16.7885 59.477 17.7052 57.419C19.0576 54.3825 20.3931 51.3396 21.7453 48.303C22.3909 46.8529 23.5788 45.9991 25.3014 45.929C28.1445 45.8131 30.2687 48.261 29.1576 50.8921C27.7091 54.3216 26.1434 57.7101 24.6285 61.1165C24.3521 61.7382 24.0772 62.3605 23.7801 63.0314C24.2092 63.3795 24.5757 63.7598 25.0242 64.0258C26.6954 65.017 28.4817 65.7478 30.506 65.9144C34.0947 66.2098 37.2482 65.4815 39.5056 62.7212C41.5034 60.2783 41.8772 57.6559 40.4039 54.8734C39.5932 53.3425 38.4358 52.0404 37.1359 50.8381C34.6009 48.4935 31.6556 46.6094 28.7373 44.6965C25.5967 42.6377 22.4437 40.5943 19.316 38.5195C18.4452 37.9419 17.6447 37.2771 16.812 36.6522C16.2078 36.0812 15.5446 35.5518 15.0138 34.9293C14.2637 34.0495 13.6115 33.1009 12.9174 32.1817C12.9045 32.1137 12.9069 32.0394 12.8765 31.9786C11.8476 29.9147 11.4662 27.7315 11.4016 25.4855C11.3013 21.9913 12.0287 18.6743 14.0017 15.6403C16.244 12.1918 19.4768 9.77352 23.4851 8.1489C26.5217 6.91818 29.7003 6.28437 33.0012 6.13835C37.615 5.93425 42.0943 6.61149 46.4615 7.94561C47.4594 8.25054 48.4478 8.58132 49.4626 8.90727C50.3527 6.90236 51.28 4.95216 52.0829 2.9605C52.6895 1.45553 53.6803 0.399987 55.4247 0Z"
                  fill="#2458A7"
                />
                <path
                  class="logo-icon"
                  d="M61.4293 17.3108H66.2065C68.3308 17.3197 70.4552 17.3357 72.5795 17.3357C74.7416 17.3356 76.9038 17.3196 79.0659 17.3105C80.1093 17.3105 81.1528 17.3105 82.2397 17.3105C82.4814 16.6209 82.7241 15.9357 82.9617 15.2491C83.6356 13.3025 85.8093 12.2428 87.9217 12.8328C90.0475 13.4266 91.2142 15.4589 90.548 17.4087C89.7385 19.778 88.9285 22.1472 88.1187 24.5164C87.5134 26.2267 86.908 27.937 86.3031 29.6473C86.2169 29.8911 86.1333 30.1356 86.0485 30.3797C86.0028 30.4334 85.9527 30.4845 85.9121 30.5412C84.7456 32.1723 82.6243 32.7534 80.7813 31.9456C78.9182 31.1292 78.0841 29.2351 78.762 27.384C79.0378 26.6307 79.2925 25.8709 79.5526 25.1128C79.6461 24.8404 79.7269 24.5645 79.8383 24.2116H66.8508C65.473 27.6652 64.0931 31.1242 62.6844 34.6553C62.9697 34.6116 63.1348 34.5891 63.2987 34.5608C67.7437 33.7909 72.1577 33.8927 76.5196 35.0112C82.5933 36.5687 87.6532 39.4903 91.2743 44.269C92.121 45.3864 92.8161 46.5986 93.581 47.7673C95.0354 50.8007 96.0776 53.9422 95.9567 57.2763C95.6538 65.6337 90.9819 72.7107 83.0579 77.1835C82.8326 77.3107 83.2826 77.0557 83.0579 77.1835C83.0075 77.2147 83.1082 77.1523 83.0579 77.1835C83.0179 77.2229 83.0979 77.1443 83.0579 77.1835C82.7679 77.285 83.3335 77.0565 83.0579 77.1835C79.4099 78.8651 74.6148 80.1694 70.5251 80.0128C62.0075 79.6865 55.0993 76.5363 50.0989 70.157C50.0774 70.1297 50.0675 70.0948 50.0293 70.0163C51.519 68.5236 53.0239 67.0157 54.5414 65.4952C55.1319 65.5267 55.7173 65.5579 56.1404 65.5805C57.2816 66.7397 58.2374 67.901 59.3971 68.8584C69.2744 77.0136 83.4219 72.2073 87.0103 62.1986C90.2255 53.2312 83.6993 43.3419 73.3653 41.5232C69.705 40.879 66.0924 41.1678 62.5053 41.92C60.9152 42.2534 59.353 42.6967 57.7781 43.0898C57.5969 43.1351 57.4151 43.1785 57.2096 43.2286C55.6099 41.9478 54.0152 40.6708 52.4036 39.3805C53.1658 37.4712 53.9153 35.5908 54.667 33.7109C56.4433 29.2687 58.2394 24.833 59.9756 20.378"
                  fill="#DE8545"
                />
              </g>

              <!-- Gradient definition for shine effect -->
              <defs>
                <linearGradient
                  id="shineGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="0%"
                >
                  <stop
                    offset="0%"
                    style="stop-color: white; stop-opacity: 0"
                  />
                  <stop
                    offset="50%"
                    style="stop-color: white; stop-opacity: 0.2"
                  />
                  <stop
                    offset="100%"
                    style="stop-color: white; stop-opacity: 0"
                  />
                </linearGradient>
              </defs>

              <!-- Shine effect overlay (hidden initially) -->
              <rect
                id="logo-shine"
                x="-20"
                y="0"
                width="20"
                height="98"
                fill="url(#shineGradient)"
                opacity="0"
              />
            </svg>
            <span class="text-white font-bold text-lg">Serv5</span>
          </div>
          <button
            id="closeMobileMenu"
            class="text-white hover:text-primary-yellow transition-colors duration-300 p-2"
            aria-label="Close mobile menu"
          >
            <i class="fa-solid fa-times text-xl"></i>
          </button>
        </div>

        <!-- Mobile Menu Navigation -->
        <nav class="p-6">
          <ul class="space-y-4 mobile-nav-items">
            <!-- Home -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-house text-lg"></i>
                <span data-nav="home" class="font-medium">الرئيسية</span>
              </a>
            </li>
            <!-- About -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-5 text-lg"></i>
                <span data-nav="about" class="font-medium">نبذة عنا</span>
              </a>
            </li>
            <!-- Services -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-plus text-lg"></i>
                <span data-nav="services" class="font-medium">خدماتنا</span>
              </a>
            </li>
            <!-- Portfolio -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-briefcase text-lg"></i>
                <span data-nav="portfolio" class="font-medium">أعمالــنا</span>
              </a>
            </li>
            <!-- Blog -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-regular fa-rectangle-list text-lg"></i>
                <span data-nav="blog" class="font-medium">المدونــة</span>
              </a>
            </li>
            <!-- Brochure -->
            <li class="mobile-nav-item">
              <a
                href="/"
                class="flex items-center gap-3 text-white hover:text-primary-yellow transition-colors duration-300 p-3 rounded-lg hover:bg-gray-800"
              >
                <i class="fa-solid fa-file-pdf text-lg"></i>
                <span data-nav="brochure" class="font-medium"
                  >الكتيب التعريفي</span
                >
              </a>
            </li>
          </ul>

          <!-- Mobile Menu Actions -->
          <div class="mt-8 space-y-4 mobile-menu-actions">
            <button
              class="w-full rounded-md flex items-center justify-center gap-2 text-white border border-white font-bold bg-transparent p-4 hover:bg-white hover:text-primary-blue transition-all duration-300"
            >
              <i class="fa-solid fa-book"></i>
              <span data-nav="profile">بروفايل الشركة</span>
            </button>

            <!-- Mobile Vision Image -->
            <div class="flex justify-center mt-6">
              <img
                src="/public/shared/header/our-vision.webp"
                alt="Serv5 future vision for 2030"
                class="w-[100px] h-[67px]"
              />
            </div>
          </div>
        </nav>
      </div>
    </header>
    <!-- end of the header -->

    <!-- start of breadcrumbs -->
    <section
      class="bottom-banner bg-light-blue lg:h-[251px] rounded-md container py-10 relative overflow-hidden"
    >
      <!-- flying icons start -->
      <div
        class="absolute flex top-1/2 -translate-y-1/2 left-1/2   lg:start-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="/public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="/public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-20 lg:end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-5 lg:bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>
        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute hidden lg:inline top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="/public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>
      <!-- flying icons end -->
      <div
        class="absolute top-1/2 -translate-y-1/2 hidden lg:flex end-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="/public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="/public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="/public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>

        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="/public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>

      <div class="flex flex-col">
        <!-- start of navigations -->
        <ul class="flex gap-2 text-white self-start text-sm items-center">
          <li>
            <a href="#" class="hover:text-primary-yellow cursor-pointer"
              >الرئيسية</a
            >
          </li>
          <li>
            <img
              src="/public/shared/cross-pages/serv5-small-logo.webp"
              alt="Serv5 logo"
              class="size-[12px]"
            />
          </li>
          <li>
            <a href="#" class="hover:text-primary-yellow cursor-pointer"
              >من نحن</a
            >
          </li>
        </ul>
        <!-- end of navigations -->
        <p class="banner-text text-white font-bold mt-10 text-2xl lg:text-4xl">
          من نحن
        </p>
      </div>
    </section>
    <!-- end of breadcrumbs -->

    <!-- start of sub header -->
    <section
      class="about-sub-header flex flex-col lg:flex-row gap-4 md:gap-5 items-center justify-between container px-4 md:px-6 lg:px-8 py-4 md:py-6 rounded-lg md:rounded-xl bg-secondary-blue text-white shadow-lg relative overflow-hidden"
    >
      <!-- Background decorative elements -->
      <div class="absolute inset-0 pointer-events-none">
        <div
          class="absolute top-0 right-0 w-32 h-32 bg-primary-yellow opacity-5 rounded-full transform translate-x-16 -translate-y-16"
        ></div>
        <div
          class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-5 rounded-full transform -translate-x-12 translate-y-12"
        ></div>
      </div>

      <!-- Section title -->
      <div class="relative z-10">
        <p class="font-semibold text-lg md:text-xl lg:text-2xl">من نحن</p>
      </div>

      <!-- Navigation menu -->
      <nav class="relative z-10">
        <ul
          class="flex items-center flex-wrap justify-center gap-y-2 gap-x-3 md:gap-x-4 lg:gap-x-6 xl:gap-x-8 2xl:gap-[50px]"
        >
          <li
            class="cursor-pointer hover:text-primary-yellow transition-all duration-300 transform hover:scale-105"
          >
            <a
              href="#"
              class="text-sm md:text-base lg:text-lg px-2 py-1 rounded-md hover:bg-white/10 transition-all duration-300"
            >
              نظرة عامة
            </a>
          </li>
          <li
            class="cursor-pointer hover:text-primary-yellow transition-all duration-300 transform hover:scale-105"
          >
            <a
              href="#"
              class="text-sm md:text-base lg:text-lg px-2 py-1 rounded-md hover:bg-white/10 transition-all duration-300"
              >المميزات</a
            >
          </li>
          <li
            class="cursor-pointer hover:text-primary-yellow transition-all duration-300 transform hover:scale-105"
          >
            <a
              href="#"
              class="text-sm md:text-base lg:text-lg px-2 py-1 rounded-md hover:bg-white/10 transition-all duration-300"
              >أحدث أعمالنا</a
            >
          </li>
          <li
            class="cursor-pointer hover:text-primary-yellow transition-all duration-300 transform hover:scale-105"
          >
            <a
              href="#"
              class="text-sm md:text-base lg:text-lg px-2 py-1 rounded-md hover:bg-white/10 transition-all duration-300"
              >حدد موقعك بنفسك</a
            >
          </li>
          <li
            class="cursor-pointer hover:text-primary-yellow transition-all duration-300 transform hover:scale-105"
          >
            <a
              href="#"
              class="text-sm md:text-base lg:text-lg px-2 py-1 rounded-md hover:bg-white/10 transition-all duration-300"
              >أعمال متعلقة بالخدمة</a
            >
          </li>
          <li
            class="cursor-pointer hover:text-primary-yellow transition-all duration-300 transform hover:scale-105"
          >
            <a
              href="#"
              class="text-sm md:text-base lg:text-lg px-2 py-1 rounded-md hover:bg-white/10 transition-all duration-300"
              >مقالات مشابهة</a
            >
          </li>
        </ul>
      </nav>

      <!-- CTA Button -->
      <div class="relative z-10">
        <button
          class="about-cta-button bg-white rounded-lg md:rounded-xl text-light-blue font-bold p-3 md:p-4 flex justify-center items-center gap-2 hover:text-white hover:bg-light-blue transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 shadow-md hover:shadow-lg min-w-[120px] md:min-w-[140px]"
        >
          <i class="fa-solid fa-arrow-right text-sm md:text-base"></i>
          <span class="text-sm md:text-base">طلب الخدمة</span>
        </button>
      </div>
    </section>
    <!-- end of sub header -->

    <!-- start section of make your bussiness easier -->
    <section class="container grid grid-cols-1 lg:grid-cols-4 gap-5 lg:gap-8">
      <!-- the image div -->
      <div class="hidden 2xl:block lg:col-span-1">
        <!-- orange container  -->
        <div
          class="border relative rounded-md w-[240px] h-[416px] border-primary-yellow mx-auto"
        >
          <!-- webdesign image -->
          <img
            src="/public/pages/about-page/web-design.webp"
            alt="Web design image"
            class="block absolute -top-12 -end-12 w-[233px] h-[257px]"
          />
          <!-- handshake image -->
          <img
            src="/public/pages/about-page/hand-shake.webp"
            alt="Handshake image"
            class="block absolute top-1/2 -translate-y-1/2 -start-12 w-[275px] h-[250px]"
          />
          <!-- serv5 logo image -->
          <img
            src="/public/pages/about-page/serv5-logo.webp"
            alt="Serv5 logo image"
            class="block absolute -end-12 -bottom-12 w-[254px] h-[227px]"
          />
        </div>
      </div>
      <!-- the content div -->
      <div class="col-span-1 lg:col-span-3 2xl:col-span-2">
        <h2
          class="business-heading font-bold text-3xl md:text-4xl lg:text-5xl text-primary-blue"
        >
          إجعل تجارتك أسهل
        </h2>
        <p
          class="business-description bg-[#DDEBFF] text-[#184385] text-sm leading-7 p-6 md:p-8 lg:p-10 mt-5 md:mt-8 lg:mt-[52px] rounded-md"
        >
          تميزنا في تصميم المواقع،برمجة المواقع،برمجة تطبيقات الجوال علاوة على
          التسويق الإلكتروني؛ فنحن شركة تدعم التطوير وتعمل على توليد أفكار
          إبداعية في شتا المجالات . لم نسعي فقط للتميز بل كان هدفنا الأول هو
          إرضاء العميل لذا أصبحتا الأفضل ، شعارنا هو القدرة و التكيف على التغيير
          , فكان سعينا الدائم هو أن نقدم حلولاً جديدة تلائم متطلبات عملاؤنا
          المتجددة . لذا نتقدم لكم الان بأحدث إصدارات شركتنا شركة سيرف فايف , من
          عام 2009 م منذ ذلك الحين تعد سيرف فايف إحدى الشركات الرائدة في مجال
          تطوير البرمجيات وتطبيقات الويب و المواقع الإلكترونية , فنحن ننتج حلول
          تقنية لقطاعات أعمال مختلفة تتميز بواجهات مرنة و سهلة الاستخدام . ولا
          نكتفي بهذا فقط فنحن نقدم أيضا لعملائنا تصميمات فريدة و أنيقة للمواقع
          الإلكترونية , كما نوفر لهم خدمات استضافة عالية الكفاءة , مما يمكنهم من
          التميز في فضاء الإنترنت . تكمن مهنية سيرف فايف في تقديم نظم تقنية
          بسيطة و فعالة لعملائنا , مما أتاح لنا توسيع نشاطاتنا و تحسين جودة
          منتجاتنا خلال فترة زمنية قصيرة و التطوير بشكل دائم , و تخطو سيرف فايف
          في الوقت الحالي نحو إنتاج نظم و تطبيقات الويب التي تعتمد بشكل أساسي
          على شبكة الإنترنت بأفضل و احدث اللغات البرمجية و التقنيات الحديثة
          المتبعة .
        </p>
      </div>
      <!-- the links div -->
      <ul
        class="business-links-list col-span-1 lg:col-span-1 flex flex-col gap-2 lg:gap-3"
      >
        <li
          class="business-link-item border border-secondary-blue rounded-md p-3 md:p-4 transition-all duration-300"
        >
          <a href="#" class="flex items-center justify-between">
            <span
              class="link-text block text-[#191C1F] text-sm md:text-base transition-all duration-300"
            >
              إنجازاتنا
            </span>
            <span
              class="link-icon rounded-full bg-secondary-blue size-[28px] md:size-[34px] items-center justify-center flex text-center transition-all duration-300"
            >
              <i
                class="fa-solid fa-arrow-up -rotate-45 text-white text-xs md:text-sm text-center transition-all duration-300"
              ></i>
            </span>
          </a>
        </li>
        <li
          class="business-link-item border border-secondary-blue rounded-md p-3 md:p-4 transition-all duration-300"
        >
          <a href="#" class="flex items-center justify-between">
            <span
              class="link-text block text-[#191C1F] text-sm md:text-base transition-all duration-300"
            >
              خدماتنا
            </span>
            <span
              class="link-icon rounded-full bg-secondary-blue size-[28px] md:size-[34px] items-center justify-center flex text-center transition-all duration-300"
            >
              <i
                class="fa-solid fa-arrow-up -rotate-45 text-white text-xs md:text-sm text-center transition-all duration-300"
              ></i>
            </span>
          </a>
        </li>
        <li
          class="business-link-item border border-secondary-blue rounded-md p-3 md:p-4 transition-all duration-300"
        >
          <a href="#" class="flex items-center justify-between">
            <span
              class="link-text block text-[#191C1F] text-sm md:text-base transition-all duration-300"
            >
              أعمالنا
            </span>
            <span
              class="link-icon rounded-full bg-secondary-blue size-[28px] md:size-[34px] items-center justify-center flex text-center transition-all duration-300"
            >
              <i
                class="fa-solid fa-arrow-up -rotate-45 text-white text-xs md:text-sm text-center transition-all duration-300"
              ></i>
            </span>
          </a>
        </li>
        <li
          class="business-link-item border border-secondary-blue rounded-md p-3 md:p-4 transition-all duration-300"
        >
          <a href="#" class="flex items-center justify-between">
            <span
              class="link-text block text-[#191C1F] text-sm md:text-base transition-all duration-300"
            >
              تواصل معنا
            </span>
            <span
              class="link-icon rounded-full bg-secondary-blue size-[28px] md:size-[34px] items-center justify-center flex text-center transition-all duration-300"
            >
              <i
                class="fa-solid fa-arrow-up -rotate-45 text-white text-xs md:text-sm text-center transition-all duration-300"
              ></i>
            </span>
          </a>
        </li>
        <li
          class="business-link-item border border-secondary-blue rounded-md p-3 md:p-4 transition-all duration-300"
        >
          <a href="#" class="flex items-center justify-between">
            <span
              class="link-text block text-[#191C1F] text-sm md:text-base transition-all duration-300"
            >
              إنجازاتنا
            </span>
            <span
              class="link-icon rounded-full bg-secondary-blue size-[28px] md:size-[34px] items-center justify-center flex text-center transition-all duration-300"
            >
              <i
                class="fa-solid fa-arrow-up -rotate-45 text-white text-xs md:text-sm text-center transition-all duration-300"
              ></i>
            </span>
          </a>
        </li>
        <li
          class="business-link-item border border-secondary-blue rounded-md p-3 md:p-4 transition-all duration-300"
        >
          <a href="#" class="flex items-center justify-between">
            <span
              class="link-text block text-[#191C1F] text-sm md:text-base transition-all duration-300"
            >
              خدماتنا
            </span>
            <span
              class="link-icon rounded-full bg-secondary-blue size-[28px] md:size-[34px] items-center justify-center flex text-center transition-all duration-300"
            >
              <i
                class="fa-solid fa-arrow-up -rotate-45 text-white text-xs md:text-sm text-center transition-all duration-300"
              ></i>
            </span>
          </a>
        </li>
        <li
          class="business-link-item border border-secondary-blue rounded-md p-3 md:p-4 transition-all duration-300"
        >
          <a href="#" class="flex items-center justify-between">
            <span
              class="link-text block text-[#191C1F] text-sm md:text-base transition-all duration-300"
            >
              أعمالنا
            </span>
            <span
              class="link-icon rounded-full bg-secondary-blue size-[28px] md:size-[34px] items-center justify-center flex text-center transition-all duration-300"
            >
              <i
                class="fa-solid fa-arrow-up -rotate-45 text-white text-xs md:text-sm text-center transition-all duration-300"
              ></i>
            </span>
          </a>
        </li>
        <li
          class="business-link-item border border-secondary-blue rounded-md p-3 md:p-4 transition-all duration-300"
        >
          <a href="#" class="flex items-center justify-between">
            <span
              class="link-text block text-[#191C1F] text-sm md:text-base transition-all duration-300"
            >
              تواصل معنا
            </span>
            <span
              class="link-icon rounded-full bg-secondary-blue size-[28px] md:size-[34px] items-center justify-center flex text-center transition-all duration-300"
            >
              <i
                class="fa-solid fa-arrow-up -rotate-45 text-white text-xs md:text-sm text-center transition-all duration-300"
              ></i>
            </span>
          </a>
        </li>
      </ul>
    </section>
    <!-- end section of make your bussiness easier -->

    <!-- start of our goals /the animated and bouncy divs -->
    <section class="container mt-10 flex flex-col gap-24 2xl:gap-0">
      <!-- first card -->
      <div class="vision-card rounded-md shadow-lg p-7 max-w-[715px] relative">
        <!-- rocket -->
        <div class="rocket-container absolute -top-16 start-5">
          <svg
            class="rocket-svg -z-10"
            width="127"
            height="151"
            viewBox="0 0 127 151"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              class="rocket-nose"
              d="M65.5966 55.2505C78.7781 56.623 90.6038 46.7875 92.01 33.2822C93.4163 19.7769 83.8705 7.71611 70.689 6.34358C57.5075 4.97105 45.6818 14.8066 44.2756 28.3118C42.8693 41.8171 52.415 53.8779 65.5966 55.2505Z"
              fill="#2458A7"
            />
            <path
              class="rocket-window-outer"
              d="M49.4054 33.3753C48.0686 22.7981 55.3909 13.0376 65.7268 11.6158C76.0626 10.194 85.5575 17.6436 86.8943 28.2208C88.2311 38.798 80.9088 48.5584 70.5729 49.9802C60.2303 51.4014 50.7354 43.9518 49.4054 33.3753ZM53.4629 32.8141C54.5069 41.0963 61.946 46.9307 70.0391 45.8202C78.1323 44.7098 83.867 37.0627 82.8231 28.7805C81.7792 20.4983 74.3401 14.664 66.2469 15.7744C58.1537 16.8848 52.419 24.532 53.4629 32.8141Z"
              fill="white"
            />
            <path
              class="rocket-window-inner"
              d="M57.982 32.1936C57.2601 26.4571 61.2231 21.166 66.8286 20.3981C72.4342 19.6301 77.5814 23.6619 78.3033 29.3984C79.0252 35.1348 75.0622 40.4259 69.4567 41.1939C63.858 41.9626 58.7115 37.9238 57.982 32.1936ZM62.0464 31.6332C62.4823 35.0753 65.5729 37.4989 68.9366 37.0353C72.3002 36.5717 74.6823 33.3947 74.2465 29.9525C73.8106 26.5104 70.7199 24.0868 67.3563 24.5504C63.9927 25.014 61.6106 28.191 62.0464 31.6332Z"
              fill="white"
            />
            <path
              class="rocket-body"
              d="M109.195 136.543L6.1825 125.816C3.69533 125.557 1.89467 123.282 2.16001 120.734L8.3663 61.1301C8.63164 58.5818 10.8624 56.7265 13.3495 56.9855L116.362 67.7117C118.849 67.9707 120.65 70.2457 120.384 72.794L114.178 132.398C113.913 134.946 111.682 136.802 109.195 136.543Z"
              fill="#2458A7"
            />
            <path
              class="rocket-fin-1"
              d="M69.5928 132.419L45.7803 129.939L44.9872 137.556L68.7997 140.036L69.5928 132.419Z"
              fill="#2458A7"
            />
            <path
              class="rocket-fin-2"
              d="M74.5282 146.372L38.0817 142.577C38.4082 139.441 41.1532 137.158 44.2143 137.476L69.5711 140.117C72.6391 140.436 74.8548 143.236 74.5282 146.372Z"
              fill="#2458A7"
            />
            <path
              d="M110.481 130.61L116.399 73.7783L12.0746 62.9155L6.15699 119.747L110.481 130.61Z"
              fill="white"
            />
            <path
              class="rocket-flame-1"
              d="M80.902 85.1512L78.799 105.348L75.752 103.368L76.6151 95.0791L71.0782 91.4738L72.3188 79.5586L80.902 85.1512Z"
              fill="#FF7F6E"
            />
            <path
              d="M74.7853 55.8758L56.48 53.9697L52.7718 89.5823L71.0771 91.4883L74.7853 55.8758Z"
              fill="white"
              stroke="#2458A7"
              stroke-width="0.316"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              d="M71.0782 91.4891L67.8385 95.0085L55.2181 93.6944L52.7729 89.583L71.0782 91.4891Z"
              fill="#2458A7"
              stroke="#2458A7"
              stroke-width="0.316"
              stroke-miterlimit="10"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
            <path
              class="rocket-flame-2"
              d="M44.4569 81.3574L42.3539 101.554L45.7435 100.244L46.6065 91.9556L52.7675 89.5683L54.0081 77.6531L44.4569 81.3574Z"
              fill="#FF7F6E"
            />
            <path
              d="M67.3147 38.7368C67.3147 38.7368 57.8047 41.2495 56.4802 53.9698L65.6294 54.9225L74.7786 55.8751C76.11 43.1556 67.3147 38.7368 67.3147 38.7368Z"
              fill="#FF7F6E"
            />
            <path
              d="M68.297 97.9081C68.1465 97.2979 67.3647 96.9122 67.3122 96.8855C68.1859 100.303 67.8989 104.717 67.8989 104.717C67.3437 103.548 66.0827 102.525 66.0827 102.525C65.4915 108.203 59.5113 113.63 59.5113 113.63C59.5113 113.63 54.7775 107.087 55.3687 101.409C55.3687 101.409 53.9451 101.948 52.895 102.878C52.895 102.878 53.7746 98.809 55.3335 95.6453C55.2766 95.6606 54.4321 95.877 54.1591 96.443C54.1591 96.443 54.1934 95.0525 55.2113 93.7007L67.8385 95.0155C68.55 96.5403 68.297 97.9081 68.297 97.9081Z"
              fill="white"
              stroke="#2458A7"
              stroke-width="0.316"
              stroke-miterlimit="10"
            />
            <path
              d="M64.0918 69.6943C66.4352 69.9383 68.5376 68.1897 68.7877 65.7887C69.0377 63.3877 67.3406 61.2435 64.9971 60.9994C62.6537 60.7554 60.5512 62.504 60.3012 64.905C60.0512 67.3061 61.7483 69.4503 64.0918 69.6943Z"
              fill="#2458A7"
              stroke="white"
              stroke-width="0.316"
              stroke-miterlimit="10"
            />
            <path
              class="rocket-exhaust-cloud"
              d="M105.328 125.597C105.293 125.593 105.259 125.597 105.224 125.593C105.017 124.135 103.876 122.955 102.386 122.8C101.519 122.709 100.696 122.985 100.063 123.492C100.142 123.196 100.208 122.891 100.241 122.576C100.553 119.58 98.4355 116.897 95.5042 116.592C94.3905 116.476 93.3291 116.719 92.4123 117.232C92.4398 117.101 92.4605 116.968 92.4743 116.835C92.6959 114.707 91.1944 112.81 89.1172 112.594C87.3611 112.411 85.7638 113.49 85.1796 115.12C84.8171 113.229 83.2876 111.732 81.3198 111.527C80.2334 111.414 79.2003 111.717 78.3658 112.302C78.8691 111.449 79.198 110.478 79.3081 109.421C79.7098 105.564 76.9813 102.116 73.2164 101.724C71.2759 101.522 69.4331 102.172 68.0571 103.381C66.5604 100.84 63.952 99.0258 60.843 98.702C57.8502 98.3904 55.0389 99.5201 53.06 101.543C51.9587 100.247 50.3939 99.3549 48.59 99.167C44.8251 98.775 41.445 101.586 41.0433 105.444C40.934 106.494 41.0552 107.518 41.3719 108.457C40.6766 107.705 39.7274 107.203 38.6409 107.09C36.6731 106.885 34.8673 108.042 34.1236 109.811C33.8871 108.102 32.5471 106.71 30.791 106.527C28.7139 106.311 26.8536 107.858 26.632 109.987C26.6182 110.12 26.6112 110.253 26.611 110.388C25.8188 109.704 24.8311 109.24 23.7173 109.124C20.7929 108.819 18.1621 111.001 17.8494 114.004C17.8165 114.319 17.8118 114.63 17.8351 114.937C17.3209 114.303 16.5716 113.871 15.7038 113.781C14.2074 113.625 12.8477 114.545 12.3512 115.93C12.3171 115.926 12.2836 115.916 12.2495 115.912C11.0537 115.788 9.98676 116.682 9.85992 117.9C9.73235 119.125 10.5998 120.213 11.7888 120.337L104.893 130.031C106.089 130.156 107.156 129.262 107.283 128.044C107.383 126.816 106.523 125.722 105.328 125.597Z"
              fill="white"
              stroke="#2458A7"
              stroke-width="0.316"
              stroke-miterlimit="10"
            />
          </svg>
        </div>
        <!-- the pin and thc circle under it -->
        <div class="pin-container absolute -top-10 end-2 w-[100px] h-[94px]">
          <img
            src="/public/pages/about-page/pin.png"
            alt="Pin image"
            class="pin-image w-full h-full absolute z-10"
          />
          <span
            class="pin-hole size-6 rounded-full absolute -bottom-1 end-4 z-0"
          ></span>
        </div>
        <div class="bg-[#F6FAFF] text-center rounded-md p-5">
          <h3 class="font-bold text-3xl">رؤيتنا</h3>
          <p class="leading-7 text-[#6C7275] mt-10">
            الريادة في إبداع و تطوير أفكار برمجية خلاقة على مستوى المنطقة
            العربية و العالم اجمع , نسعى لتقديم خدمات برمجية تقنية محترفة عالية
            الجودة و باسعار منافسة , تتجاوز توقعات العملاء , و قد حرصت سيرف فايف
            منذ تأسيسها على تحقيق اعلى مستوى جودة
          </p>
        </div>
      </div>
      <!-- second card -->
      <div
        class="vision-card rounded-md shadow-lg p-7 max-w-[715px] relative ms-auto"
      >
        <!-- envloap -->
        <div class="envelope-container absolute -top-16 end-5 z-10">
          <svg
            class="envelope-svg z-10"
            width="239"
            height="214"
            viewBox="0 0 239 214"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              class="envelope-background"
              d="M130.23 167.635C161.248 162.75 182.09 131.465 176.781 97.7588C171.473 64.0522 142.024 40.6878 111.006 45.5728C79.9879 50.4579 59.1461 81.7426 64.4546 115.449C69.763 149.156 99.2115 172.52 130.23 167.635Z"
              fill="#DBE8EC"
            />
            <path
              d="M201.136 80.8462L200.129 74.4516C200.034 73.8478 199.83 73.2671 199.53 72.7428C199.229 72.2185 198.838 71.7607 198.378 71.3956C197.917 71.0305 197.398 70.7653 196.848 70.6151C196.298 70.4649 195.729 70.4326 195.174 70.5201L180.24 72.8721C179.684 72.9596 179.115 72.9274 178.565 72.7771C178.016 72.6269 177.496 72.3617 177.036 71.9966C176.576 71.6316 176.184 71.1738 175.884 70.6495C175.583 70.1251 175.38 69.5445 175.284 68.9407L174.277 62.546C174.182 61.9422 174.197 61.3271 174.322 60.7358C174.447 60.1445 174.679 59.5886 175.005 59.0997C175.33 58.6109 175.743 58.1988 176.22 57.8869C176.697 57.575 177.229 57.3694 177.784 57.2819L178.256 57.2076C179.378 57.0309 180.378 56.377 181.036 55.3898C181.693 54.4026 181.955 53.1629 181.763 51.9435L180.756 45.5489C180.564 44.3294 179.934 43.2302 179.004 42.4929C178.075 41.7556 176.923 41.4407 175.8 41.6174L55.4929 60.5647C54.3707 60.7414 53.3709 61.3953 52.7132 62.3825C52.0555 63.3697 51.7939 64.6094 51.9859 65.8288L52.993 72.2235C53.1851 73.4429 53.815 74.5421 54.7443 75.2794C55.6736 76.0167 56.826 76.3316 57.9482 76.1549C59.0703 75.9782 60.2228 76.2931 61.1521 77.0304C62.0814 77.7677 62.7113 78.8669 62.9033 80.0863L63.9104 86.481C64.1025 87.7004 63.8409 88.9401 63.1832 89.9273C62.5255 90.9145 61.5256 91.5684 60.4035 91.7451L44.0417 94.3219C42.9195 94.4987 41.9196 95.1525 41.2619 96.1398C40.6043 97.127 40.3427 98.3666 40.5347 99.586L41.5418 105.981C41.6369 106.585 41.8405 107.165 42.141 107.689C42.4415 108.214 42.8329 108.672 43.2931 109.037C43.7532 109.402 44.273 109.667 44.8227 109.817C45.3724 109.967 45.9413 110 46.497 109.912L52.9171 108.901C53.4727 108.814 54.0416 108.846 54.5914 108.996C55.1411 109.146 55.6609 109.411 56.121 109.777C56.5811 110.142 56.9726 110.599 57.2731 111.124C57.5736 111.648 57.7772 112.229 57.8723 112.832L58.8794 119.227C59.0714 120.447 58.8098 121.686 58.1521 122.673C57.4944 123.661 56.4946 124.315 55.3724 124.491L55.3569 124.494C54.2347 124.67 53.2349 125.324 52.5772 126.312C51.9195 127.299 51.6579 128.538 51.85 129.758L52.857 136.152C53.0491 137.372 53.679 138.471 54.6083 139.208C55.5376 139.946 56.6901 140.261 57.8122 140.084L58.7411 139.938C59.8632 139.761 61.0157 140.076 61.9449 140.813C62.8742 141.55 63.5042 142.65 63.6962 143.869L64.7033 150.264C64.7984 150.868 64.7831 151.483 64.6583 152.074C64.5336 152.665 64.3017 153.221 63.9761 153.71C63.6504 154.199 63.2373 154.611 62.7604 154.923C62.2835 155.235 61.752 155.44 61.1964 155.528L45.9521 157.929C44.83 158.105 43.8301 158.759 43.1724 159.747C42.5147 160.734 42.2531 161.973 42.4452 163.193L43.4523 169.587C43.6443 170.807 44.2743 171.906 45.2035 172.643C46.1328 173.381 47.2853 173.696 48.4074 173.519L195.259 150.391C196.381 150.214 197.381 149.561 198.039 148.573C198.697 147.586 198.958 146.347 198.766 145.127L197.759 138.732C197.567 137.513 196.937 136.414 196.008 135.676C195.078 134.939 193.926 134.624 192.804 134.801L189.135 135.379C188.58 135.466 188.011 135.434 187.461 135.284C186.911 135.134 186.392 134.868 185.931 134.503C185.471 134.138 185.08 133.68 184.779 133.156C184.479 132.632 184.275 132.051 184.18 131.447L183.173 125.053C182.981 123.833 183.243 122.594 183.9 121.606C184.558 120.619 185.558 119.965 186.68 119.789L195.33 118.426C196.452 118.249 197.452 117.596 198.11 116.608C198.767 115.621 199.029 114.382 198.837 113.162L197.83 106.767C197.638 105.548 197.008 104.449 196.079 103.712C195.149 102.974 193.997 102.659 192.875 102.836L190.251 103.249C189.129 103.426 187.976 103.111 187.047 102.374C186.118 101.637 185.488 100.537 185.296 99.3179L184.288 92.9232C184.096 91.7038 184.358 90.4641 185.016 89.4769C185.673 88.4897 186.673 87.8358 187.795 87.6591L197.629 86.1104C198.185 86.0229 198.716 85.8173 199.193 85.5054C199.67 85.1935 200.083 84.7813 200.409 84.2925C200.734 83.8037 200.966 83.2478 201.091 82.6565C201.216 82.0651 201.231 81.45 201.136 80.8462Z"
              fill="#DBE8EC"
            />
            <path
              d="M70.6222 123.733C73.089 123.345 74.7465 120.857 74.3243 118.176C73.9022 115.496 71.5602 113.638 69.0934 114.026C66.6267 114.415 64.9692 116.903 65.3913 119.583C65.8135 122.264 68.1555 124.122 70.6222 123.733Z"
              fill="#B9D4DB"
            />
            <path
              d="M75.8103 130.421C77.0894 130.219 77.9488 128.929 77.7299 127.539C77.511 126.149 76.2967 125.186 75.0176 125.387C73.7386 125.589 72.8791 126.879 73.098 128.269C73.3169 129.659 74.5313 130.622 75.8103 130.421Z"
              fill="#B9D4DB"
            />
            <path
              d="M70.7156 138.95C71.9947 138.748 72.8541 137.458 72.6352 136.068C72.4163 134.679 71.202 133.715 69.9229 133.917C68.6438 134.118 67.7844 135.408 68.0033 136.798C68.2222 138.188 69.4365 139.151 70.7156 138.95Z"
              fill="#B9D4DB"
            />
            <path
              d="M64.5249 131.344C66.3522 131.057 67.5799 129.214 67.2672 127.228C66.9545 125.242 65.2197 123.866 63.3925 124.154C61.5653 124.442 60.3375 126.284 60.6502 128.27C60.9629 130.256 62.6977 131.632 64.5249 131.344Z"
              fill="#B9D4DB"
            />
            <path
              d="M149.927 76.5683C152.394 76.1798 154.052 73.6918 153.63 71.0112C153.207 68.3306 150.865 66.4725 148.399 66.861C145.932 67.2495 144.274 69.7375 144.697 72.4181C145.119 75.0987 147.461 76.9568 149.927 76.5683Z"
              fill="#B9D4DB"
            />
            <path
              d="M139.884 76.2526C141.163 76.0512 142.023 74.7611 141.804 73.3712C141.585 71.9813 140.37 71.0178 139.091 71.2192C137.812 71.4207 136.953 72.7107 137.172 74.1007C137.391 75.4906 138.605 76.4541 139.884 76.2526Z"
              fill="#B9D4DB"
            />
            <path
              d="M134.333 67.7634C135.612 67.5619 136.472 66.2719 136.253 64.8819C136.034 63.492 134.82 62.5285 133.541 62.73C132.262 62.9314 131.402 64.2215 131.621 65.6114C131.84 67.0013 133.054 67.9648 134.333 67.7634Z"
              fill="#B9D4DB"
            />
            <path
              d="M144.167 65.8765C145.994 65.5887 147.222 63.7458 146.909 61.7602C146.596 59.7746 144.861 58.3982 143.034 58.686C141.207 58.9737 139.979 60.8167 140.292 62.8023C140.605 64.7879 142.339 66.1643 144.167 65.8765Z"
              fill="#B9D4DB"
            />
            <path
              class="envelope-flap-top"
              d="M150.671 120.025L84.8813 130.386C83.2879 130.637 77.7493 99.973 78.8393 98.7114L107.671 67.7325C107.926 67.4377 108.266 67.2457 108.639 67.1871C109.011 67.1285 109.394 67.2064 109.727 67.4087L146.685 88.0264C148.11 88.8919 152.265 119.774 150.671 120.025Z"
              fill="#E06714"
            />
            <path
              class="envelope-flap-left"
              d="M86.4765 151.417L78.491 100.712C78.4314 100.333 78.4821 99.9468 78.6362 99.6065C78.7903 99.2661 79.04 98.9891 79.3504 98.8141C79.6609 98.6391 80.0167 98.5748 80.3679 98.6304C80.7192 98.6859 81.0485 98.8583 81.3098 99.1237L113.825 119.984C114.105 120.267 114.291 120.641 114.354 121.046C114.418 121.45 114.356 121.863 114.177 122.218L89.6472 152.063C89.4801 152.395 89.2197 152.661 88.9025 152.822C88.5853 152.982 88.2271 153.031 87.8778 152.959C87.5286 152.888 87.2058 152.702 86.9546 152.425C86.7033 152.148 86.5362 151.796 86.4765 151.417Z"
              fill="#DE8545"
            />
            <path
              class="envelope-flap-right"
              d="M155.619 140.528L147.633 89.823C147.573 89.4441 147.406 89.0917 147.155 88.8152C146.904 88.5387 146.581 88.3518 146.232 88.2807C145.883 88.2096 145.524 88.2578 145.207 88.4186C144.89 88.5794 144.63 88.8447 144.462 89.1775L119.932 119.022C119.754 119.377 119.692 119.79 119.755 120.195C119.819 120.599 120.005 120.973 120.284 121.256L152.8 142.117C153.061 142.382 153.39 142.554 153.742 142.61C154.093 142.665 154.449 142.601 154.759 142.426C155.07 142.251 155.319 141.974 155.473 141.634C155.628 141.293 155.678 140.907 155.619 140.528Z"
              fill="#DE8545"
            />
            <path
              d="M154.229 142.615L88.4396 152.976C88.0908 153.031 87.7322 152.966 87.4136 152.791C87.0951 152.615 86.8327 152.338 86.663 151.997C86.4933 151.657 86.4249 151.27 86.4672 150.891C86.5095 150.512 86.6605 150.16 86.899 149.884L115.731 118.905C115.986 118.61 116.326 118.418 116.699 118.359C117.071 118.301 117.454 118.379 117.787 118.581L154.745 139.199C155.057 139.388 155.309 139.677 155.465 140.024C155.622 140.372 155.676 140.761 155.619 141.137C155.562 141.514 155.398 141.858 155.149 142.123C154.899 142.388 154.578 142.56 154.229 142.615Z"
              fill="#DE8545"
            />
            <path
              class="envelope-stamp"
              d="M79.39 106.42C85.7684 105.416 90.0543 98.9824 88.9627 92.0511C87.8711 85.1198 81.8154 80.3152 75.4369 81.3198C69.0585 82.3243 64.7726 88.7576 65.8642 95.6889C66.9558 102.62 73.0115 107.425 79.39 106.42Z"
              fill="#F9AE2B"
            />
            <path
              d="M76.7203 85.5498L75.5171 85.7393L77.3454 97.3481L78.5485 97.1586L76.7203 85.5498Z"
              fill="#2458A7"
            />
            <path
              d="M78.709 102.098C80.1606 101.869 79.777 99.4178 78.323 99.6468C76.8714 99.8754 77.255 102.327 78.709 102.098Z"
              fill="#2458A7"
            />
            <path
              d="M167.584 82.0771L178.034 80.4314"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M168.099 85.3506L178.549 83.7049"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M168.614 88.6221L179.063 86.9764"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M68.1499 155.295L78.5995 153.649"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M68.665 158.567L79.1146 156.922"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M69.1797 161.84L79.6293 160.194"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M84.0508 66.3281L94.5004 64.6824"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M84.5659 69.6016L95.0155 67.9559"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M85.0806 72.875L95.5302 71.2293"
              stroke="#B9D4DB"
              stroke-width="3"
              stroke-miterlimit="10"
              stroke-linecap="round"
            />
            <path
              d="M189.26 131.831C191.727 131.442 193.385 128.954 192.963 126.274C192.54 123.593 190.198 121.735 187.732 122.124C185.265 122.512 183.607 125 184.03 127.681C184.452 130.361 186.794 132.219 189.26 131.831Z"
              fill="#B9D4DB"
            />
            <path
              d="M177.656 134.854C178.935 134.653 179.795 133.363 179.576 131.973C179.357 130.583 178.142 129.619 176.863 129.821C175.584 130.022 174.725 131.312 174.944 132.702C175.163 134.092 176.377 135.056 177.656 134.854Z"
              fill="#B9D4DB"
            />
            <path
              d="M181.285 119.627C183.112 119.34 184.34 117.497 184.027 115.511C183.714 113.526 181.979 112.149 180.152 112.437C178.325 112.725 177.097 114.568 177.41 116.553C177.723 118.539 179.457 119.915 181.285 119.627Z"
              fill="#B9D4DB"
            />
            <path
              d="M169.857 109.464L162.153 118.854L146.929 137.409L143.726 149.102C143.688 149.242 143.606 149.363 143.495 149.446C143.383 149.528 143.249 149.566 143.113 149.554L143.083 149.55C142.952 149.532 142.829 149.469 142.733 149.37C142.636 149.271 142.572 149.141 142.548 149.001L141.229 138.604L140.756 134.881L169.011 108.507C169.128 108.405 169.277 108.355 169.429 108.368C169.581 108.381 169.725 108.455 169.831 108.575C169.938 108.695 170 108.854 170.004 109.018C170.009 109.183 169.957 109.342 169.857 109.464Z"
              fill="#15205C"
            />
            <path
              d="M170.003 109.095L170.002 109.105C169.986 109.275 169.908 109.43 169.786 109.536L164.041 114.552L140.755 134.885L128.604 128.546C128.498 128.491 128.408 128.404 128.346 128.297C128.283 128.189 128.249 128.065 128.249 127.94C128.249 127.814 128.283 127.692 128.345 127.588C128.408 127.483 128.497 127.402 128.603 127.352L169.133 108.424L169.15 108.416L169.181 108.403L169.215 108.39L169.246 108.382L169.285 108.373L169.318 108.368L169.323 108.369L169.356 108.366L169.392 108.365C169.415 108.365 169.439 108.367 169.462 108.37C169.493 108.375 169.524 108.381 169.554 108.391C169.568 108.395 169.581 108.4 169.595 108.405C169.609 108.411 169.624 108.417 169.638 108.424L169.682 108.447C169.691 108.452 169.7 108.458 169.708 108.464C169.712 108.466 169.715 108.468 169.718 108.471L169.744 108.489L169.75 108.49L169.772 108.508C169.792 108.525 169.81 108.543 169.827 108.562C169.835 108.57 169.843 108.579 169.85 108.589C169.875 108.619 169.897 108.651 169.915 108.685C169.925 108.701 169.933 108.718 169.941 108.735C169.948 108.749 169.953 108.764 169.959 108.779C169.965 108.794 169.973 108.818 169.98 108.838C169.985 108.855 169.989 108.873 169.993 108.891C169.998 108.912 170.001 108.934 170.003 108.956C170.004 108.97 170.005 108.984 170.007 108.997C170.008 109.03 170.006 109.063 170.003 109.095Z"
              fill="#2458A7"
            />
            <mask
              id="mask0_1586_8446"
              style="mask-type: luminance"
              maskUnits="userSpaceOnUse"
              x="126"
              y="102"
              width="45"
              height="51"
            >
              <path
                d="M130.569 102.888L126.352 147.23L166.479 152.794L170.696 108.452L130.569 102.888Z"
                fill="white"
              />
            </mask>
            <g mask="url(#mask0_1586_8446)">
              <path
                d="M166.948 147.536C166.941 147.622 166.918 147.706 166.881 147.782C166.843 147.858 166.792 147.925 166.73 147.979C166.668 148.033 166.597 148.072 166.52 148.095C166.442 148.119 166.362 148.125 166.282 148.113C166.216 148.104 166.152 148.084 166.092 148.053L145.434 137.284L164.041 114.551L168.941 108.578C168.996 108.51 169.064 108.456 169.141 108.418C169.153 108.413 169.165 108.408 169.177 108.403L169.211 108.391L169.242 108.382L169.281 108.374L169.314 108.369L169.319 108.369L169.352 108.367L169.388 108.366C169.412 108.366 169.435 108.368 169.458 108.371C169.489 108.376 169.52 108.382 169.55 108.392C169.564 108.396 169.578 108.401 169.591 108.406C169.606 108.411 169.62 108.418 169.634 108.425L169.678 108.448C169.687 108.453 169.696 108.459 169.704 108.465C169.708 108.466 169.712 108.469 169.714 108.472L169.74 108.49L169.746 108.491L169.769 108.509C169.788 108.526 169.806 108.544 169.823 108.563C169.832 108.571 169.839 108.58 169.847 108.59C169.871 108.62 169.893 108.652 169.912 108.686C169.921 108.702 169.929 108.719 169.937 108.736C169.944 108.75 169.95 108.765 169.956 108.779C169.962 108.794 169.97 108.818 169.976 108.839C169.981 108.856 169.985 108.874 169.989 108.892C169.994 108.913 169.997 108.935 170 108.957C170 108.971 170.001 108.984 170.003 108.998C170.005 109.03 170.005 109.063 170.002 109.095L170.001 109.104L166.948 147.536Z"
                fill="#2458A7"
              />
              <path
                d="M151.582 140.488L143.585 149.364C143.521 149.435 143.443 149.488 143.356 149.52C143.269 149.553 143.176 149.563 143.084 149.55C142.999 149.539 142.917 149.507 142.843 149.459C142.725 149.382 142.633 149.266 142.583 149.128C142.532 148.99 142.526 148.838 142.564 148.698L145.436 137.284L151.582 140.488Z"
                fill="#4469A0"
              />
            </g>
            <path
              d="M145.436 137.286L151.583 140.491L150.419 141.782L145.119 138.544L145.436 137.286Z"
              fill="#4469A0"
            />
          </svg>
        </div>
        <!-- the pin and thc circle under it -->
        <div class="pin-container absolute -top-10 start-2 w-[100px] h-[94px]">
          <img
            src="/public/pages/about-page/pin.png"
            alt="Pin image"
            class="pin-image w-full h-full absolute z-10"
          />
          <span
            class="pin-hole size-6 rounded-full absolute -bottom-1 end-4 z-0"
          ></span>
        </div>
        <div class="bg-[#F6FAFF] text-center rounded-md p-5 relative -z-10">
          <h3 class="font-bold text-3xl relative z-30">رسالتنا</h3>
          <p class="leading-7 text-[#6C7275] mt-10 relative z-30">
            لقد وصلت لوجهتك الصحيحة؛ ما تبحث عنه امامك معنا سوف تحصل على خدمة
            تصميم مواقع ، برمجة مواقع ، برمجة تطبيقات جوال ،
          </p>
        </div>
      </div>
      <!-- third card -->
      <div class="vision-card rounded-md shadow-lg p-7 max-w-[715px] relative">
        <!-- man looking at future -->
        <div class="future-vision-container absolute -top-16 end-5">
          <svg
            class="future-vision-svg -z-10"
            width="194"
            height="200"
            viewBox="0 0 194 200"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              class="person-face"
              d="M89.047 97.0069C89.2294 101.548 88.8301 106.107 87.8571 110.53C87.5628 111.857 87.1722 113.248 86.1779 114.125C84.8131 115.329 82.7947 115.16 81.0114 114.881C81.0008 114.879 80.9872 114.877 80.9765 114.874C80.9546 116.005 81.0438 117.242 81.0752 118.476C81.0891 118.981 81.0848 119.503 80.8782 119.953C80.6929 120.355 80.3524 120.659 79.9867 120.888C79.964 120.902 79.9418 120.915 79.9197 120.928C79.4038 121.239 78.8228 121.434 78.2261 121.499C77.5426 121.576 76.8475 121.476 76.1631 121.35C73.7513 120.903 71.3986 120.079 69.2086 118.917C69.333 109.221 65.4218 102.656 65.8748 99.93C66.1027 98.5661 66.6502 97.2867 67.1537 96.0091C67.8694 94.2129 68.5162 92.3823 69.427 90.6865C70.3422 88.9938 71.54 87.4222 73.1365 86.4157C74.9728 85.2596 77.2252 84.9219 79.3657 85.2072C83.4136 85.7498 87.2395 88.6881 88.4612 92.7519C88.8695 94.1243 88.9876 95.5708 89.047 97.0069Z"
              fill="#FFC5A6"
            />
            <path
              class="person-hair"
              d="M71.4701 83.7013C71.4701 83.7013 64.4473 81.1522 61.7232 87.8525C58.999 94.5522 61.6267 99.7284 63.6681 103.271C65.7095 106.813 68.4666 112.494 68.6283 113.822C68.6283 113.822 73.377 110.638 74.14 104.013C74.14 104.013 69.9522 103.07 71.0291 98.8041C72.1065 94.5385 77.9889 96.4494 77.6504 105.951C77.6504 105.951 78.9142 99.84 80.1655 97.8123C81.4167 95.784 82.5855 95.3936 86.6917 95.6102C90.7944 95.8266 97.5326 92.5173 96.4292 83.4538C96.4292 83.4538 92.2404 85.0736 88.7979 84.5886C83.7663 83.8855 76.9561 77.2257 71.4701 83.7013Z"
              fill="#203752"
            />
            <path
              d="M81.0717 118.477C81.0857 118.982 81.0809 119.504 80.8742 119.955C80.6889 120.357 80.3485 120.66 79.9827 120.889L75.543 112.661C77.668 114.101 80.976 114.875 80.976 114.875C80.976 114.875 81.04 117.24 81.0717 118.477Z"
              fill="#FF9F6B"
            />
            <path
              d="M139.226 104.211C140.759 102.407 142.132 100.453 143.321 98.3861C143.951 97.2914 144.533 96.1531 144.88 94.9266C145.086 94.1928 145.2 93.3734 144.875 92.6826C144.632 92.1663 144.179 91.796 143.728 91.4643C142.982 90.9173 142.163 90.4106 141.259 90.3039C139.691 90.119 138.271 91.15 137.015 92.1326C136.463 92.5653 135.899 93.0062 135.509 93.6031C135.117 94.204 134.92 94.927 134.831 95.65C134.554 97.8713 135.225 100.112 136.068 102.187C136.35 102.88 136.671 103.592 137.235 104.066C137.798 104.54 138.745 104.776 139.226 104.211Z"
              fill="#FFC5A6"
            />
            <path
              d="M114.206 90.4238C110.049 92.7241 105.896 95.024 101.74 97.3243C101.552 97.4282 101.355 97.5376 101.227 97.7201C101.039 97.9875 101.046 98.348 101.079 98.6774C101.201 99.8468 101.611 100.981 102.302 102.081C102.5 102.398 102.87 102.559 103.223 102.476C108.121 101.359 112.998 100.148 117.855 98.8512C116.851 96.0232 115.786 93.2137 114.206 90.4238Z"
              fill="#15205C"
            />
            <path
              d="M152.83 92.799C140.522 96.0632 128.024 97.8437 115.973 101.041C115.318 101.213 114.637 100.866 114.361 100.219C113.022 97.0884 111.949 94.0456 111.409 90.8701C111.323 90.3595 111.509 89.791 111.958 89.5765C121.848 84.8693 133.497 79.0934 147.973 72.2073C150.1 78.8272 151.641 85.6498 152.83 92.799Z"
              fill="#15205C"
            />
            <path
              d="M134.111 105.103C134.057 103.098 133.92 101.051 133.171 99.1992C132.607 97.8003 131.703 96.5415 131.364 95.0687C131.153 94.1509 131.177 93.1979 131.208 92.2579C131.221 91.9162 131.233 91.5638 131.376 91.2589C131.518 90.9511 131.82 90.7013 132.148 90.7395C132.365 90.7663 132.553 90.9135 132.715 91.0711C133.559 91.8937 133.955 93.083 134.55 94.1132C135.173 95.1901 136.037 96.1168 137.054 96.7979C137.679 97.2144 138.379 97.5546 138.837 98.1657C139.307 98.7857 139.461 99.6038 139.482 100.392C139.536 102.339 138.773 104.361 137.246 105.465C136.662 105.887 135.959 106.168 135.251 106.095C134.542 106.021 133.842 105.521 134.111 105.103Z"
              fill="#FFC5A6"
            />
            <path
              d="M122.467 105.817C122.448 99.8946 128.016 95.2845 128.25 89.3666C128.26 89.0888 128.255 88.7917 128.104 88.5583C127.974 88.3574 127.756 88.2388 127.543 88.1406C124.946 86.953 121.726 87.849 119.825 90.0056C117.923 92.1626 117.308 95.3716 117.931 98.2497C118.55 101.132 120.306 103.673 122.467 105.817Z"
              fill="#FFC5A6"
            />
            <path
              d="M104.662 89.04C100.506 91.3403 96.3531 93.6401 92.1961 95.9405C92.0088 96.045 91.8111 96.1538 91.6837 96.3363C91.4959 96.6036 91.5022 96.9648 91.5353 97.2936C91.6573 98.463 92.0674 99.5974 92.7584 100.698C92.9564 101.014 93.3262 101.175 93.6797 101.093C98.5778 99.9752 103.455 98.7649 108.311 97.468C107.308 94.64 106.242 91.8299 104.662 89.04Z"
              fill="#15205C"
            />
            <path
              d="M143.287 91.4113C130.978 94.6755 118.48 96.456 106.429 99.6532C105.774 99.8255 105.093 99.4794 104.817 98.8321C103.478 95.7007 102.405 92.6579 101.865 89.483C101.779 88.9718 101.965 88.4033 102.414 88.1888C112.304 83.4816 123.953 77.7057 138.429 70.8196C140.556 77.4431 142.097 84.2627 143.287 91.4113Z"
              fill="#15205C"
            />
            <path
              d="M124.095 96.7658C122.958 96.3476 121.748 95.8746 121.03 94.8588C120.275 93.7912 120.241 92.3702 120.237 91.0478C120.231 89.6022 120.228 88.1593 120.222 86.7131C120.219 86.2741 120.204 85.7864 119.92 85.456C119.52 84.9939 118.803 85.074 118.253 85.3043C116.708 85.9529 115.635 87.4312 114.828 88.948C113.725 91.0218 112.964 93.451 113.495 95.7761C113.81 97.1635 114.573 98.4359 114.735 99.8486C114.875 101.058 114.567 102.266 114.47 103.48C114.373 104.694 114.549 106.052 115.423 106.867C116.392 107.774 118.05 107.632 118.925 106.643C119.242 106.285 119.459 105.844 119.675 105.41C121.1 102.552 122.522 99.6915 124.095 96.7658Z"
              fill="#FFC5A6"
            />
            <path
              d="M142.71 102.296C138.712 101.659 134.601 101.872 130.907 103.283C130.374 110.075 129.354 116.822 127.865 123.45C127.13 126.709 126.118 130.197 123.507 132.136C120.221 134.575 115.663 133.665 111.821 132.271C101.867 128.664 92.6856 122.478 82.3071 120.663C80.7521 120.391 80.1755 122.689 78.6099 122.606C73.2736 122.334 72.7205 118.71 62.9841 121.218C60.3764 121.535 56.2891 123.288 54.1861 125.1C50.6317 128.161 49.636 133.349 49.0607 138.138C48.3606 143.968 47.9544 149.84 47.9393 155.827C48.7377 162.637 49.047 169.503 48.7322 176.935C58.0299 177.324 67.3439 177.123 76.6062 176.329C81.9555 175.872 87.337 175.206 92.4078 173.412C92.8027 164.436 93.1952 155.463 93.7119 146.62C101.373 147.213 109.034 147.808 116.696 148.4C121.895 148.803 127.628 149.048 131.71 145.717C135.088 142.962 136.506 138.393 137.626 134.066C140.331 123.634 141.478 113.354 142.71 102.296Z"
              fill="#2458A7"
            />
            <path
              d="M76.2353 124.39C77.2836 126.805 78.2095 129.277 79.0793 132.122C79.2957 145.091 79.0709 158.06 78.4073 171.002C78.394 171.284 78.3796 171.584 78.5061 171.838C78.5874 172.005 78.7189 172.138 78.85 172.267C79.8786 173.291 80.9104 174.314 81.9387 175.341C82.7368 174.248 83.5346 173.158 84.3326 172.065C84.5855 171.718 84.8476 171.356 84.9447 170.93C85.0228 170.591 84.9891 170.233 84.9534 169.883C83.7666 157.652 82.5803 145.422 81.3969 133.191C81.2847 132.017 81.1682 130.836 81.2217 129.656C81.2814 128.308 81.564 126.972 81.4948 125.622C81.4171 124.118 80.9152 122.676 80.4167 121.258C80.3694 121.121 80.3144 120.979 80.1965 120.902C80.0641 120.818 79.8975 120.845 79.745 120.878C79.0001 121.036 78.2514 121.192 77.5059 121.35C77.2243 121.409 76.9211 121.484 76.7499 121.722C76.6365 121.881 76.6042 122.087 76.5739 122.285C76.4749 122.953 76.3795 123.617 76.2353 124.39Z"
              fill="#15205C"
            />
            <path
              d="M81.0749 118.881C80.5666 119.897 79.8114 120.556 78.5165 121.026C75.4765 120.414 71.9659 119.341 69.2828 117.785C68.8544 118.731 68.536 119.659 68.2978 120.493C70.8377 123.528 73.7242 126.251 76.8721 128.587C77.2478 126.424 77.8737 124.313 78.7335 122.308C79.9783 123.823 81.2416 125.58 82.3881 128.107C82.7861 124.732 82.5792 122.194 81.0749 118.881Z"
              fill="#2458A7"
            />
            <path
              d="M125.243 103.193C120.887 102.677 116.476 102.697 111.947 103.223C110.982 107.794 110.02 112.368 109.054 116.94C108.501 119.563 107.941 122.217 106.829 124.635C105.717 127.054 103.987 129.254 101.647 130.372C99.3737 131.458 96.7684 131.43 94.2578 131.343C89.6987 131.178 85.1428 130.867 80.5992 130.406C77.5655 130.098 74.186 129.822 71.8125 131.785C71.0778 132.391 70.5001 133.171 69.9735 133.98C68.6257 136.052 67.576 138.396 67.3607 140.897C67.1421 143.399 67.8475 146.067 69.5783 147.839C71.751 150.06 75.0311 150.494 78.0533 150.736C87.7002 151.509 97.4258 151.566 106.989 150.178C110.251 149.703 113.674 148.971 116.082 146.649C117.669 145.12 118.659 143.038 119.439 140.936C121.092 136.48 121.942 131.745 122.692 127.027C123.934 119.17 124.93 111.277 125.243 103.193Z"
              fill="#2458A7"
            />
            <path
              d="M53.1567 75.4352C51.9046 80.8319 50.758 86.2588 49.7189 91.704C49.1592 94.6406 48.6238 97.61 48.6865 100.609C43.6687 97.536 39.4081 93.122 36.3545 87.9904C40.0273 78.6469 44.4855 69.6438 49.6683 61.1233C52.33 62.9516 55.0034 64.7644 57.9333 66.787C62.0961 57.6176 66.7039 48.6707 71.8749 40.0844C70.1725 38.43 68.6974 36.5282 67.5065 34.4464C72.9245 31.9039 78.4141 29.5301 83.9688 27.3291C83.8244 34.7398 83.6682 42.2194 81.9881 49.4021C80.5994 48.0429 79.2146 46.6832 77.6875 45.3835C73.402 57.9575 69.1141 70.5355 64.8291 83.1092C61.0726 80.8826 57.3161 78.6554 53.1567 75.4352Z"
              fill="#DE8545"
            />
            <path
              d="M138.589 70.9637C135.918 71.6472 139.313 91.9273 143.124 91.6086C146.802 91.2994 142.826 69.8803 138.589 70.9637Z"
              fill="#2458A7"
            />
            <path
              d="M147.744 72.246C145.073 72.9295 148.469 93.2094 152.279 92.8908C155.958 92.5852 151.981 71.1626 147.744 72.246Z"
              fill="#2458A7"
            />
            <path
              class="vision-bubble-outer"
              d="M115.871 72.6519C128.653 71.0958 137.699 59.0184 136.074 45.6763C134.45 32.3342 122.771 22.7797 109.989 24.3358C97.2065 25.8919 88.1609 37.9692 89.7852 51.3114C91.4094 64.6535 103.088 74.208 115.871 72.6519Z"
              fill="#C1D5EE"
            />
            <path
              class="vision-bubble-inner"
              d="M115.202 67.1687C125.084 65.9657 132.076 56.6291 130.821 46.3147C129.565 36.0003 120.537 28.614 110.655 29.817C100.773 31.02 93.7811 40.3566 95.0367 50.671C96.2924 60.9853 105.32 68.3716 115.202 67.1687Z"
              fill="#2458A7"
            />
            <path
              d="M114.524 61.5891C121.454 60.7455 126.357 54.1986 125.477 46.9659C124.596 39.7333 118.265 34.5539 111.336 35.3975C104.407 36.2409 99.5033 42.788 100.384 50.0206C101.264 57.2533 107.596 62.4325 114.524 61.5891Z"
              fill="white"
            />
            <path
              d="M113.832 55.9038C117.753 55.4265 120.528 51.7215 120.03 47.6286C119.531 43.5356 115.949 40.6046 112.027 41.0819C108.106 41.5593 105.331 45.2643 105.829 49.3573C106.328 53.4502 109.91 56.3812 113.832 55.9038Z"
              fill="#2458A7"
            />
            <path
              class="vision-eye"
              d="M113.229 50.9421C114.525 50.7844 115.442 49.5599 115.278 48.2072C115.113 46.8544 113.929 45.8857 112.633 46.0434C111.337 46.2012 110.42 47.4257 110.584 48.7785C110.749 50.1312 111.933 51.0999 113.229 50.9421Z"
              fill="#FFA800"
            />
            <path
              d="M101.621 89.2644C102.118 90.9769 102.615 92.6889 103.113 94.4051C103.353 95.234 103.593 96.0628 103.833 96.8917C103.962 97.3336 104.09 97.7748 104.219 98.2167C104.306 98.5147 104.387 98.8171 104.564 99.0721C104.984 99.6734 105.689 99.5482 106.296 99.4532C107.123 99.3277 107.948 99.1884 108.773 99.0421C110.475 98.7386 112.172 98.394 113.859 98.0114C114.279 97.9179 114.2 97.2147 113.777 97.3122C112.253 97.6574 110.721 97.9715 109.183 98.2509C108.415 98.3903 107.643 98.5236 106.873 98.6458C106.5 98.7051 106.125 98.7792 105.749 98.8177C105.299 98.8622 105.087 98.7385 104.942 98.2957C104.443 96.7963 104.055 95.2478 103.614 93.7267C103.158 92.1514 102.701 90.5761 102.244 89.0002C102.121 88.5759 101.497 88.8328 101.621 89.2644Z"
              fill="#203752"
            />
            <path
              d="M92.2755 100.273C92.5518 101.233 93.5706 101.506 94.4161 101.364C95.5179 101.18 96.6091 100.767 97.6825 100.459C99.997 99.7983 102.312 99.1371 104.626 98.4759C105.043 98.3579 104.79 97.7007 104.376 97.8182C102.38 98.3874 100.384 98.9602 98.3879 99.5294C97.4031 99.8127 96.418 100.092 95.4337 100.375C94.7015 100.585 93.2248 101.151 92.9 100.017C92.7796 99.5846 92.1517 99.842 92.2755 100.273Z"
              fill="#203752"
            />
            <path
              d="M113.085 98.3719C113.238 99.35 113.692 100.23 114.368 100.929C114.5 101.061 114.741 101.004 114.84 100.868C114.962 100.704 114.908 100.508 114.776 100.375C114.252 99.8359 113.877 99.0701 113.755 98.294C113.725 98.1063 113.565 97.9624 113.381 97.9813C113.204 98.0065 113.055 98.1843 113.085 98.3719Z"
              fill="#203752"
            />
            <path
              d="M101.673 101.935C102.052 102.903 103.074 102.899 103.905 102.72C105.03 102.48 106.153 102.209 107.27 101.927C109.482 101.371 111.678 100.749 113.854 100.062C114.269 99.9298 114.016 99.2767 113.604 99.4044C111.716 99.9997 109.816 100.547 107.903 101.046C106.948 101.293 105.988 101.527 105.03 101.75C104.554 101.861 104.079 101.976 103.598 102.069C103.15 102.156 102.513 102.226 102.3 101.682C102.133 101.258 101.509 101.515 101.673 101.935Z"
              fill="#203752"
            />
            <path
              d="M122.955 96.1499C128.89 94.8385 134.825 93.5272 140.76 92.2165C141.508 92.0511 142.261 91.9097 143.002 91.7131C143.61 91.55 144.2 91.2834 144.556 90.7226C144.792 90.3495 144.257 89.9112 144.021 90.2909C143.699 90.7985 143.142 90.9691 142.597 91.0954C141.907 91.2575 141.211 91.4027 140.516 91.5545C139.101 91.8684 137.685 92.1794 136.269 92.4934C133.41 93.1252 130.552 93.7569 127.694 94.3887C126.086 94.7435 124.482 95.0986 122.874 95.4541C122.45 95.5445 122.529 96.2441 122.955 96.1499Z"
              fill="#203752"
            />
            <path
              d="M137.385 96.5687C140.459 95.8962 143.534 95.2241 146.608 94.5558C148.144 94.22 149.683 93.8904 151.217 93.548C152.266 93.3139 153.341 92.9984 153.895 91.9634C154.106 91.5722 153.481 91.2972 153.271 91.6878C152.705 92.7388 151.152 92.8431 150.146 93.0648C148.72 93.3764 147.29 93.689 145.864 94.0006C143.009 94.6254 140.157 95.2492 137.3 95.8698C136.88 95.9597 136.962 96.6626 137.385 96.5687Z"
              fill="#203752"
            />
            <path
              d="M134.463 73.0721C135.815 72.6806 136.944 71.6566 138.342 71.4229C138.985 71.3162 139.681 71.373 140.199 71.8316C140.751 72.3176 141.06 73.1023 141.378 73.759C142.761 76.6364 143.696 79.742 144.134 82.919C144.384 84.7186 144.476 86.5368 144.404 88.3501C144.388 88.7993 145.056 88.9055 145.073 88.4533C145.205 84.9944 144.78 81.5284 143.805 78.208C143.317 76.5403 142.691 74.9178 141.931 73.3618C141.595 72.6687 141.262 71.9111 140.711 71.3724C140.229 70.9022 139.591 70.6893 138.944 70.6761C137.18 70.6355 135.837 71.9439 134.21 72.4155C133.797 72.5366 134.049 73.1933 134.463 73.0721Z"
              fill="#203752"
            />
            <path
              d="M143.255 74.8991C144.511 74.2215 145.782 73.5844 147.075 72.9907C147.667 72.7201 148.255 72.4925 148.875 72.8146C149.364 73.0667 149.734 73.5361 150.035 73.9993C150.762 75.1199 151.273 76.4405 151.732 77.7002C152.23 79.0616 152.622 80.4604 152.904 81.8838C153.216 83.4695 153.391 85.0791 153.435 86.6979C153.446 87.1504 154.119 87.0686 154.105 86.62C154.02 83.4921 153.427 80.3805 152.357 77.4468C151.868 76.1127 151.321 74.7118 150.535 73.5305C149.894 72.5665 148.916 71.7312 147.732 71.9963C146.939 72.174 146.169 72.6368 145.438 72.9915C144.617 73.3928 143.806 73.8111 142.999 74.2458C142.623 74.4478 142.869 75.1058 143.255 74.8991Z"
              fill="#203752"
            />
            <path
              d="M146.479 74.311C146.331 80.3177 147.575 86.3256 150.13 91.7448C150.322 92.1505 150.95 91.8967 150.758 91.491C148.258 86.1891 147.001 80.3003 147.147 74.4136C147.158 73.9658 146.489 73.8596 146.479 74.311Z"
              fill="#203752"
            />
            <path
              d="M137.319 72.9197C136.884 78.7241 138.687 84.3516 140.509 89.7748C140.653 90.2039 141.281 89.9465 141.137 89.521C139.357 84.2205 137.564 78.7012 137.99 73.022C138.021 72.5717 137.352 72.4684 137.319 72.9197Z"
              fill="#203752"
            />
            <path
              d="M68.5903 121.87C70.9433 124.565 73.5111 127.047 76.2732 129.281C76.4278 129.408 76.7314 129.364 76.7895 129.14C77.3788 126.895 78.1168 124.699 79.0022 122.567C78.808 122.59 78.6137 122.614 78.4229 122.637C79.7285 124.57 81.0375 126.503 82.343 128.44C82.5091 128.685 82.9171 128.579 82.9457 128.281C83.1795 125.905 82.9639 123.52 82.3237 121.219C82.2029 120.783 81.575 121.041 81.6954 121.473C82.3042 123.654 82.4993 125.928 82.2775 128.178C82.4781 128.125 82.6792 128.072 82.8797 128.02C81.5742 126.087 80.2657 124.154 78.9597 122.217C78.8102 121.994 78.4776 122.052 78.3804 122.288C77.4959 124.423 76.757 126.616 76.1676 128.862C76.3389 128.816 76.513 128.77 76.6839 128.721C73.9707 126.526 71.443 124.093 69.1298 121.446C68.8382 121.116 68.298 121.533 68.5903 121.87Z"
              fill="#203752"
            />
            <path
              d="M60.4836 126.798C67.5748 126.364 74.2755 129.643 81.1841 130.806C84.8813 131.43 88.6634 131.718 92.4025 131.681C94.3093 131.662 96.2156 131.529 98.0979 131.222C99.8398 130.939 101.635 130.557 103.197 129.69C106.036 128.114 107.249 124.913 108.212 121.885C108.347 121.46 107.726 121.178 107.588 121.61C106.755 124.229 105.758 127.144 103.443 128.724C101.982 129.721 100.2 130.133 98.4983 130.439C96.6772 130.767 94.8244 130.925 92.9751 130.97C89.2424 131.059 85.4538 130.775 81.7579 130.19C78.3256 129.647 74.9981 128.63 71.6419 127.712C67.9663 126.706 64.2159 125.868 60.4052 126.099C59.9667 126.123 60.0484 126.823 60.4836 126.798Z"
              fill="#203752"
            />
            <path
              d="M77.1219 127.503C77.4228 128.331 77.7281 129.163 78.0296 129.991C78.0951 130.167 78.3 130.26 78.4687 130.193C78.6398 130.123 78.7204 129.917 78.6573 129.738C78.3564 128.909 78.0511 128.078 77.7496 127.249C77.6841 127.072 77.4793 126.98 77.3111 127.047C77.1394 127.117 77.0588 127.322 77.1219 127.503Z"
              fill="#203752"
            />
            <path
              d="M81.1109 127.085C80.8758 128.163 80.7719 129.268 80.7914 130.375C80.7944 130.567 80.9979 130.705 81.1657 130.688C81.3634 130.664 81.4643 130.488 81.4618 130.301C81.4462 129.253 81.5572 128.207 81.7796 127.191C81.8219 127.005 81.6595 126.819 81.4901 126.794C81.2988 126.757 81.1532 126.899 81.1109 127.085Z"
              fill="#203752"
            />
            <path
              d="M62.741 148.221C72.408 150.278 82.2349 151.777 92.1016 151.735C97.0077 151.712 101.911 151.303 106.727 150.376C109.041 149.931 111.473 149.486 113.635 148.493C115.543 147.619 117.109 146.166 118.301 144.4C120.793 140.707 121.287 136.144 122.199 131.831C122.291 131.391 121.624 131.291 121.531 131.728C120.745 135.437 120.325 139.402 118.507 142.747C117.511 144.582 116.14 146.241 114.382 147.313C112.345 148.558 109.88 149.014 107.587 149.488C98.1729 151.435 88.4695 151.365 78.9343 150.253C73.5276 149.624 68.1618 148.66 62.8296 147.526C62.4074 147.435 62.3155 148.131 62.741 148.221Z"
              fill="#203752"
            />
            <path
              d="M78.9399 150.767C78.8081 154.289 78.6735 157.812 78.5417 161.335C78.4753 163.081 78.4099 164.83 78.3436 166.575C78.3107 167.448 78.2778 168.321 78.2453 169.197C78.2123 170.045 78.122 170.918 78.1571 171.768C78.1945 172.632 78.8925 173.118 79.4662 173.644C80.204 174.324 80.9429 175.007 81.6813 175.686C82.0066 175.987 82.4181 175.433 82.0894 175.133C81.4775 174.569 80.8651 174.006 80.2532 173.442C79.7098 172.94 78.827 172.414 78.8284 171.587C78.8329 169.923 78.9525 168.253 79.0153 166.593C79.1411 163.245 79.2669 159.896 79.3955 156.547C79.4679 154.656 79.5393 152.761 79.6116 150.869C79.6248 150.421 78.9561 150.314 78.9399 150.767Z"
              fill="#203752"
            />
            <path
              d="M83.1602 174.247C83.6705 173.557 84.1842 172.867 84.695 172.177C85.1005 171.631 85.5104 171.145 85.4815 170.408C85.4463 169.561 85.2458 168.696 85.1301 167.855C85.0111 167.015 84.8983 166.174 84.7854 165.333C84.3354 161.942 83.9416 158.539 83.6018 155.134C83.5572 154.682 82.8872 154.76 82.9318 155.212C83.2537 158.442 83.6263 161.67 84.0488 164.888C84.2563 166.483 84.4776 168.076 84.712 169.668C84.7703 170.062 84.8984 170.529 84.7403 170.906C84.5898 171.261 84.2824 171.579 84.0603 171.878C83.5815 172.522 83.1061 173.165 82.6273 173.808C82.3589 174.171 82.8942 174.606 83.1602 174.247Z"
              fill="#203752"
            />
          </svg>
        </div>
        <!-- the pin and thc circle under it -->
        <div class="pin-container absolute -top-10 start-2 w-[100px] h-[94px]">
          <img
            src="/public/pages/about-page/pin.png"
            alt="Pin image"
            class="pin-image w-full h-full absolute z-10"
          />
          <span
            class="pin-hole size-6 rounded-full absolute -bottom-1 end-4 z-0"
          ></span>
        </div>
        <div class="bg-[#F6FAFF] text-center rounded-md p-5">
          <h3 class="font-bold text-3xl">أهدافنا</h3>
          <p class="leading-7 text-[#6C7275] mt-10">
            لقد وصلت لوجهتك الصحيحة؛ ما تبحث عنه امامك معنا سوف تحصل على خدمة
            تصميم مواقع ، برمجة مواقع ، برمجة تطبيقات جوال ، خدمات تسويقية
            متميزة.
          </p>
        </div>
      </div>
    </section>
    <!-- end of our gorals /the animated and bouncy divs -->

    <!-- start of our features section -->
    <section class="features-section container overflow-hidden">
      <div
        class="w-full bg-[#f9f7fc] bg-cover bg-center bg-no-repeat px-4 md:px-6 lg:px-8 flex flex-col xl:flex-row rounded-lg"
      >
        <div class="hidden xl:block xl:w-[300px] 2xl:w-[502px] self-end">
          <img
            src="/public/pages/about-page/saudiman-holding-laptop.webp"
            alt="Saudi man holding a laptop"
            class="w-full h-full object-contain"
          />
        </div>
        <div class="space-y-4 md:space-y-6 flex-1 py-8 md:py-10">
          <!-- section header -->
          <h2
            class="features-title text-primary-blue font-bold text-2xl md:text-3xl lg:text-4xl xl:text-5xl relative"
          >
            <span
              class="absolute -bottom-2 md:-bottom-3 start-0 bg-primary-yellow h-1 w-12 md:w-16"
            ></span>
            مميزات سيرف
          </h2>
          <p class="features-subtitle text-gray text-sm md:text-base">
            قم بكتابة بياناتك و سيتم الرد عليك خلال 24 ساعة كحد أقصي
          </p>
          <ul
            class="features-grid grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4"
          >
            <!-- first feature -->
            <li
              class="feature-item p-3 md:p-4 group flex items-center gap-2 md:gap-3 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[60px] md:size-[70px] lg:size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white flex-shrink-0"
              >
                <img
                  src="/public/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-8 md:size-9 lg:size-10"
                />
              </div>
              <p
                class="transition-all duration-300 text-sm md:text-base flex-1"
              >
                دعم فني متواصل
              </p>
              <div
                class="feature-hand absolute end-0 inset-y-0 w-[120px] md:w-[140px] lg:w-[151px] hidden lg:block"
              >
                <img
                  src="/public/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                  class="w-full h-full object-contain"
                />
              </div>
            </li>
            <!-- second feature -->
            <li
              class="feature-item p-3 md:p-4 group flex items-center gap-2 md:gap-3 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[60px] md:size-[70px] lg:size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white flex-shrink-0"
              >
                <img
                  src="/public/pages/home-page/serv5-features/key-settings.webp"
                  alt="Key Settings"
                  class="size-8 md:size-9 lg:size-10"
                />
              </div>
              <p
                class="transition-all duration-300 text-sm md:text-base flex-1"
              >
                إعدادات متقدمة
              </p>
              <div
                class="feature-hand absolute end-0 inset-y-0 w-[120px] md:w-[140px] lg:w-[151px] hidden lg:block"
              >
                <img
                  src="/public/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                  class="w-full h-full object-contain"
                />
              </div>
            </li>
            <!-- third feature -->
            <li
              class="feature-item p-3 md:p-4 group flex items-center gap-2 md:gap-3 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[60px] md:size-[70px] lg:size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white flex-shrink-0"
              >
                <img
                  src="/public/pages/home-page/serv5-features/search-web.webp"
                  alt="Search Web"
                  class="size-8 md:size-9 lg:size-10"
                />
              </div>
              <p
                class="transition-all duration-300 text-sm md:text-base flex-1"
              >
                بحث متقدم
              </p>
              <div
                class="feature-hand absolute end-0 inset-y-0 w-[120px] md:w-[140px] lg:w-[151px] hidden lg:block"
              >
                <img
                  src="/public/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                  class="w-full h-full object-contain"
                />
              </div>
            </li>
            <!-- fourth feature -->
            <li
              class="feature-item p-3 md:p-4 group flex items-center gap-2 md:gap-3 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[60px] md:size-[70px] lg:size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white flex-shrink-0"
              >
                <img
                  src="/public/pages/home-page/serv5-features/target.webp"
                  alt="Target"
                  class="size-8 md:size-9 lg:size-10"
                />
              </div>
              <p
                class="transition-all duration-300 text-sm md:text-base flex-1"
              >
                استهداف دقيق
              </p>
              <div
                class="feature-hand absolute end-0 inset-y-0 w-[120px] md:w-[140px] lg:w-[151px] hidden lg:block"
              >
                <img
                  src="/public/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                  class="w-full h-full object-contain"
                />
              </div>
            </li>
            <!-- fifth feature -->
            <li
              class="feature-item p-3 md:p-4 group flex items-center gap-2 md:gap-3 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[60px] md:size-[70px] lg:size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white flex-shrink-0"
              >
                <img
                  src="/public/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-8 md:size-9 lg:size-10"
                />
              </div>
              <p
                class="transition-all duration-300 text-sm md:text-base flex-1"
              >
                تحليل البيانات
              </p>
              <div
                class="feature-hand absolute end-0 inset-y-0 w-[120px] md:w-[140px] lg:w-[151px] hidden lg:block"
              >
                <img
                  src="/public/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                  class="w-full h-full object-contain"
                />
              </div>
            </li>
            <!-- sixth feature -->
            <li
              class="feature-item p-3 md:p-4 group flex items-center gap-2 md:gap-3 bg-white rounded-md relative text-primary-blue transition-all duration-300 hover:bg-primary-blue hover:text-white shadow-md"
            >
              <div
                class="feature-icon flex items-center justify-center size-[60px] md:size-[70px] lg:size-[82px] rounded-full bg-light-gray transition-all duration-300 group-hover:bg-white flex-shrink-0"
              >
                <img
                  src="/public/pages/home-page/serv5-features/graph.webp"
                  alt="Graph"
                  class="size-8 md:size-9 lg:size-10"
                />
              </div>
              <p
                class="transition-all duration-300 text-sm md:text-base flex-1"
              >
                دعم فني متواصل
              </p>
              <div
                class="feature-hand absolute end-0 inset-y-0 w-[120px] md:w-[140px] lg:w-[151px] hidden lg:block"
              >
                <img
                  src="/public/pages/home-page/serv5-features/bg-white-gray-hand.webp"
                  alt="Hand"
                  class="w-full h-full object-contain"
                />
              </div>
            </li>
          </ul>
        </div>
      </div>
    </section>
    <!-- end of our features section -->

    <!-- start section of know more about serv5 -->
    <section
      class="bottom-banner bg-secondary-blue lg:h-[339px] gap-5 sm:gap-10 flex flex-col md:flex-row justify-between items-center sm:px-4 lg:px-24 mt-40 lg:mt-0 pt-10 relative overflow-hidden"
    >
      <!-- flying icons start -->
      <div
        class="absolute hidden lg:flex top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 lg:start-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>
        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>
      <!-- flying icons end -->
      <div
        class="absolute top-1/2 -translate-y-1/2 hidden lg:flex end-24 gap-10 items-center justify-evenly w-[580px] h-[218px] z-0"
      >
        <!-- the chat icon -->
        <img
          src="public/pages/home-page/serv5-features/chat.webp"
          alt="Chat"
          class="floating-icon w-[65px] h-[40px] object-contain float-4 absolute top-0 start-0"
        />
        <!-- the book icon -->
        <img
          src="public/pages/home-page/serv5-features/book.webp"
          alt="Book"
          class="floating-icon w-[75px] h-[70px] object-contain float-1 absolute top-0 end-10"
        />
        <!-- the envloap icon -->
        <img
          src="public/pages/home-page/serv5-features/envloap.webp"
          alt="Envelope"
          class="floating-icon w-[40px] h-[36px] object-contain float-3 absolute bottom-10 end-0"
        />
        <!-- the red envloap icon -->
        <img
          src="public/pages/home-page/serv5-features/red-envloap.webp"
          alt="Envelope"
          class="floating-icon w-[60px] h-[55px] object-contain float-2 absolute bottom-0 end-10"
        />
        <!-- gray dots -->
        <div
          class="floating-icon float-2 size-3 rounded-full bg-gray absolute top-1/3 end-5"
        ></div>

        <div
          class="floating-icon float-3 size-2 rounded-full bg-gray absolute top-1/3 start-1/4"
        ></div>
        <div
          class="floating-icon float-1 size-4 rounded-full bg-gray absolute top-1/2 start-1/4"
        ></div>
        <!-- orange dots -->
        <div
          class="floating-icon float-4 size-3 rounded-full bg-[#ed835c] absolute top-1/4 start-1/2"
        ></div>
        <div
          class="floating-icon float-1 size-5 rounded-full bg-[#ed835c] absolute bottom-5 start-1/3"
        ></div>
        <div
          class="floating-icon float-3 size-1.5 rounded-full bg-[#ed835c] absolute top-5 start-1/2"
        ></div>

        <!-- the dotted sine wave -->
        <div
          class="absolute top-1/2 -translate-y-1/2 inset-x-0 w-full overflow-hidden"
        >
          <img
            src="public/pages/home-page/serv5-features/dotted-line.svg"
            alt="Dotted line"
          />
        </div>
      </div>
      <!-- text section know more about serv 5 -->
      <div class="flex flex-col gap-5">
        <h3 class="banner-text text-white font-bold text-2xl lg:text-4xl">
          هل تريد معرفة المزيد عن سيرف؟
        </h3>

        <p class="max-w-[537px] font-light text-white leading-6">
          معنا سوف تحصل على اقوي استضافة مواقع تتيح لك العديد من المزايا وتساعدك
          في الحفاظ على آمن موقعك وتجعله بعيداً عن القرصنة .
        </p>

        <button
          class="banner-button rounded-md bg-white font-bold text-primary-blue p-4 hover:text-white hover:bg-primary-blue text-sm transition-all duration-300 hover:outline shadow-lg w-fit"
        >
          <i class="fa-solid fa-file-pdf"></i>
          تحميل الكتيب التعريفي
        </button>
      </div>

      <!-- serv5 logo -->
      <div
        class="bg-white self-stretch items-center justify-center aspect-square rotate-45 rounded-md hidden 2xl:flex"
      >
        <img
          src="/public/pages/about-page/serv5-logo-small.webp"
          alt="Serv5 Logo"
          class="w-[217px] h-[202px] -rotate-45"
        />
      </div>

      <!-- image of saudi man carrying serv5 logo -->
      <div class="relative">
        <div
          class="banner-image -translate-y-[70px] lg:-translate-y-[85px] sm:h-[421px]"
        >
          <img
            src="public/pages/home-page/serv5-features/saudi-man.webp"
            alt="Saudi man carrying serv5 logo"
            class="shrink-0 object-contain sm:h-[421px]"
          />

          <img
            src="public/pages/home-page/serv5-features/serv-logo.webp"
            alt="serv5 logo"
            class="banner-logo size-[53px] absolute top-20 sm:top-36 end-16 sm:end-28"
          />
        </div>
      </div>
    </section>
    <!-- end section of know more about serv5 -->

    <!-- start of the footer -->
    <footer class="bg-primary-blue text-white">
      <!-- first part -->
      <div
        class="grid grid-cols-2 sm:grid-cols-3 gap-x-2 gap-y-5 lg:grid-cols-4 xl:grid-cols-7 lg:gap-4 xl:gap-3 container pt-[93px] pb-12"
      >
        <!-- first col -->
        <div class="space-y-5 col-span-2 xl:col-span-2">
          <div class="flex items-center gap-6">
            <img
              src="/public/pages/home-page/footer/serv5-logo.svg"
              alt="Serv5 Logo"
              class="size-[98px]"
            />
            <p
              class="font-bold text-xl border-b border-b-primary-yellow pb-2 max-w-[300px]"
            >
              عن سيرف
            </p>
          </div>

          <p class="text-sm leading-7 max-2-[410px]">
            لقد وصلت لوجهتك الصحيحة؛ ما تبحث عنه امامك معنا سوف تحصل على خدمة
            تصميم مواقع ، برمجة مواقع ، برمجة تطبيقات جوال ، خدمات تسويقية
            متميزة.
          </p>
          <!-- the images of our projects  -->
          <div class="flex gap-2 items-center">
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/blmgan.webp"
                alt="Blmgan app"
                class="object-contain w-[52px]"
              />
            </div>
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/makok.webp"
                alt="Makok Website"
                class="object-contain w-[52px]"
              />
            </div>
            <div
              class="w-[70px] h-[32px] bg-white rounded-md flex itecems-center justify-center hover:scale-105 cursor-pointer"
            >
              <img
                src="/public/pages/home-page/footer/needbots.webp"
                alt="Chat app neat bot"
                class="object-contain w-[52px]"
              />
            </div>
          </div>
        </div>
        <!-- second col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            روابط هامة
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- third col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            الخدمات
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
            <li>
              <a href="#" class="sm hover:text-primary-yellow cursor-pointer"
                >عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- fourth col -->
        <div class="flex flex-col gap-4">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            تواصل معنا
          </p>

          <ul class="mt-4 lg:mt-10 flex flex-col gap-3">
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-envelope text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-phone text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
            <li>
              <a
                href="#"
                class="sm hover:text-primary-yellow cursor-pointer group"
              >
                <i
                  class="fa-solid fa-location-pin-lock text-primary-yellow me-2 group-hover:scale-110"
                ></i>
                عن سيرف</a
              >
            </li>
          </ul>
        </div>
        <!-- fifth col -->
        <div class="flex flex-col gap-4 col-span-1 xl:col-span-2">
          <p
            class="font-bold text-xl border-b w-fit border-b-primary-yellow pb-2"
          >
            إشترك معنا
          </p>

          <p class="mt-4 lg:mt-10 flex flex-col gap-3">
            قم بكتابة بريدك الإلكتروني وإشترك في النشرة البريدية
          </p>

          <!-- Newsletter Subscription Form -->
          <form id="newsletter-form" class="md:mt-6 max-w-[350px]">
            <div class="newsletter-input-group relative">
              <i
                class="fa-solid fa-arrow-right absolute text-primary-yellow top-1/2 -translate-y-1/2"
              ></i>
              <input
                type="email"
                id="newsletter-email"
                name="email"
                placeholder="أدخل بريدك الإلكتروني"
                class="w-full bg-transparent text-white placeholder-gray-300 py-3 border-0 border-b border-gray-400 ps-5 placeholder:text-sm focus:border-primary-yellow focus:outline-none transition-colors duration-300"
                required
              />
              <div
                id="email-error"
                class="text-red-400 text-xs mt-1 hidden"
              ></div>
            </div>

            <div
              id="newsletter-success"
              class="text-green-400 text-sm mt-2 hidden"
            >
              تم الاشتراك بنجاح! شكراً لك.
            </div>
          </form>
        </div>
      </div>
      <!-- second part -->
      <div
        class="container flex flex-col gap-5 sm:flex-row justify-between -mt-20 items-center py-5 w-full border-t border-t-gray"
      >
        <!-- go up and terms and condtions links -->
        <div class="flex gap-4 items-center">
          <button
            id="goToTopBtn"
            class="size-12 p-5 text-white bg-primary-yellow hover:scale-110 transition-colors cursor-pointer flex items-center justify-center"
          >
            <i class="fa-solid fa-arrow-up text-xl"></i>
          </button>

          <a href="#" class="hover:text-primary-yellow">سياسة الخصوصية </a>
          <div class="w-[1px] h-6 bg-white my-auto"></div>
          <a href="#" class="hover:text-primary-yellow">الشروط والأحكــام</a>
        </div>

        <!-- all right reserved text -->
        <p class="text-sm text-center">
          © 2025 جميع الحقوق محفوظة لشركة سيرف. جميع الحقوق محفوظة.
        </p>
      </div>
    </footer>
    <!-- end of the footer -->

    <script type="module" src="/src/main.js"></script>
  </body>
</html>
